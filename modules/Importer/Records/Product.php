<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 30.10.2018 г.
 * Time: 13:07 ч.
 */

namespace Modules\Importer\Records;

use App\Applications\Managers\Stores;
use App\Helper\Format;
use App\Helper\Temp\ProductTemp;
use App\Helper\YesNo;
use App\Models\Category\Property;
use App\Models\Category\PropertyCategory;
use App\Models\Category\PropertyOption;
use App\Models\Category\PropertyValue;
use App\Models\Discount\Discount as DiscountModel;
use App\Models\Discount\ProductToDiscount;
use App\Models\Product\Product as ProductModel;
use App\Models\Product\Quantity;
use App\Models\Product\Status;
use App\Models\Product\Variant as VariantModel;
use App\Models\Queue\SiteQueue;
use App\Models\System\AppsManager;
use App\Models\System\ExternalMetaData;
use Apps;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Modules\Apps\Administration\GroceryStore\GroceryStoreManager;
use Modules\Apps\Administration\GroceryStore\Models\Units;
use Modules\Apps\Administration\Suppliers\Models\SupplierProducts;
use Modules\Importer\DataFormat\Discount as DiscountDataFormatter;
use Modules\Importer\DataFormat\Product as ProductDataFormatter;
use Modules\Importer\DataFormat\Shop as ShopFormatter;
use Modules\Importer\DataFormat\Tag;
use Modules\Importer\Jobs\Importer;
use Modules\Importer\Jobs\PropertyValueFallback;
use Throwable;

class Product extends AbstractRecord
{
    /** @var Importer|null $cli */
    protected static $cli;

    /**
     * @param ProductDataFormatter|null $formatter
     * @param null|ProductModel $record
     * @param bool $populate_temp
     * @return array|null [ProductModel, new]
     * @throws \Exception
     */
    public static function firstOrCreate(?ProductDataFormatter $formatter = null, ?ProductModel $record = null, $populate_temp = true): null|array|int
    {
        if (is_null($record)) {
            $start = microtime(true);
            if (empty($record = static::findProduct($formatter))) {
                return null;
            }

            self::info(sprintf('Product find for %s sec', microtime(true) - $start));
        }

        if ($record->exists) {
            return static::editProduct($record, $formatter, $populate_temp);
        } else {
            return static::createProduct($formatter, $populate_temp);
        }
    }

    /**
     * @param ProductDataFormatter|null $formatter
     * @param bool $populate_temp
     * @return array|null|int [ProductModel, new]
     * @throws \Exception
     */
    protected static function createProduct(?ProductDataFormatter $formatter = null, $populate_temp = true): int|array
    {
        if ($formatter->getForUpdate()) {
            return -1;
        }

        VariantModel::resetStaticVariants();

        $product_info = [
            'name' => $formatter->getName(),
            'url_handle' => $formatter->getUrlHandle() ?: $formatter->getName(),
            'seo_title' => $formatter->getSeoTitle(),
            'seo_description' => $formatter->getSeoDescription(),
            'description' => $formatter->getDescription(),
            'short_description' => $formatter->getShortDescription(),
            'type' => ProductModel::TYPE_SIMPLE,
            'app_import' => static::getAppImportKey($formatter),
            'category_id' => null,
            'vendor_id' => null,
            'imported' => YesNo::True,
            'sale' => YesNo::False,
            'xml_import_id' => $formatter->getXmlImportId(),
            'xml_import_product_id' => $formatter->getXmlImportProductId(),
            'xml_import_name' => $formatter->getXmlImportName(),
            'minimum' => $formatter->getMinimum() > 0 ? $formatter->getMinimum() : 1,
        ];

        if ($formatter->getTags()) {
            $product_info['tags'] = implode(',', array_map(fn(Tag $tag) => $tag->getTag(), $formatter->getTags()));
        }

        if ($formatter->isShipping()) {
            $product_info['require_shipping_address'] = YesNo::True;
        }

        if ($formatter->isNew()) {
            $product_info['new'] = YesNo::True;
        }

        if ($formatter->isFeatured()) {
            $product_info['featured'] = YesNo::True;
        }

        if ($formatter->isTracking()) {
            $product_info['track_inventory'] = YesNo::True;
            $product_info['threshold'] = 2;
        } else {
            $formatter->setContinueSelling(false);
        }

        if ($formatter->isContinueSelling()) {
            $product_info['continue_selling'] = YesNo::True;
        }

        static::setStockStatus($product_info, $formatter);
        static::setHidden($product_info, $formatter);

        if (!empty($category_id = $formatter->getCategoryId())) {
            $product_info['category_id'] = $category_id;
        } elseif ($formatter->getCategory() && !empty($category = Category::firstOrCreate($formatter->getCategory()))) {
            $product_info['category_id'] = $category[0]->id;
        }

        if (!empty($vendor_id = $formatter->getVendorId())) {
            $product_info['vendor_id'] = $vendor_id;
        } elseif ($formatter->getVendor() && !empty($vendor = Vendor::firstOrCreate($formatter->getVendor()))) {
            $product_info['vendor_id'] = $vendor[0]->id;
        }

        [$variants_info, $variants_data] = static::formatVariants($formatter, null, true);
        $product_info = array_merge($product_info, $variants_info);

        if (Apps::installed(GroceryStoreManager::APP_KEY)) {
            static::setUnit($product_info, $formatter);
        }

        $variantImages = static::exportVariantsImages($product_info);

        $url_handle = $product_info['url_handle'];
        /** @var ProductModel $record */
        $record = static::saveDuplicateEntryCheck(function ($attempt, $duplicate) use ($product_info, $formatter, $url_handle): \App\Models\Product\Product {
            if ($attempt && $duplicate) {
                $product_info['url_handle'] .= '-' . mt_rand();
            }

            VariantModel::resetStaticVariants();
            return ProductModel::add($product_info, $formatter->isDraft(), false, false, false);
        });

        if ($formatter->issetStatus()) {
            $record->changeStatus((bool)$formatter->isStatus());
        }

        $record->load(['variant', 'variants']);
        static::createIntegrationRecord($record, $formatter->getImportKey(), $formatter->getId());

        if (($formatter->getImage() || $formatter->getImages()) && !$record->hasImage()) {
            $imageCount = 0;
            if ($image = $formatter->getImage()) {
                $record->uploadImageFromUrl($image, 'file', ['name' => $record->name, 'sort_order' => $imageCount++]);
            }

            if ($images = $formatter->getImages()) {
                foreach ($images as $image) {
                    $record->uploadImageFromUrl($image, 'file', ['name' => $record->name, 'sort_order' => $imageCount++]);
                }
            }
        }

        static::addCategories($record, $formatter);

        static::addSuppliers($record, $formatter);

        static::setQuantitiesPerShop($record, $formatter);

        if (!empty($variants_info['variants']) && array_filter(array_column($variants_info['variants'], 'shops'))) {
            $compare = $record->variants->pluck('id', 'compare_key');
            $shops = array_map(function ($v) use ($compare) {
                if (empty($v['shops'])) {
                    return;
                }

                $v['id'] = $compare->get($v['key']);
                return $v;
            }, $variants_info['variants']);

            static::setQuantitiesPerVariantShop($record, $shops);
        }

        static::addRedirect($record, $formatter);

        static::addDiscount($record, $formatter, $variants_data, false, false);

        static::addVariantsExternalMetaData($record, $variants_data);

        static::addMeta($record, $formatter);

        static::addTabs($record, $formatter);

        // add Category Properties
        static::attachCategoryProperties(true, $record, $formatter);

        if (Apps::installed('brand_model') && $formatter->issetBrandModel()) {
            static::addBrandModel($record, $formatter);
        }

        if ($populate_temp) {
            ProductTemp::updateTempTableAndPopulateByProduct($record->id);
        }

        if (!is_null($variantImages)) {
            foreach ($variantImages as $image) {
                SiteQueue::executeQueueTaskNew([
                    'job' => 'product_variants_images',
                    'parameters' => [
                        'product_id' => $record->id,
                        'image' => $image['image'],
                        'keys' => $image['keys'],
                    ],
                ]);
            }
        }

        return [$record, true];
    }

    /**
     * @param null|ProductModel $record
     * @param ProductDataFormatter|null $formatter
     * @param bool $populate_temp
     * @return array|null [ProductModel, new]
     * @throws \Exception
     */
    public static function editProduct(ProductModel $record, ?ProductDataFormatter $formatter = null, $populate_temp = true): ?array
    {

        if ($record->is_bundle) {
            return null;
        }

        VariantModel::resetStaticVariants();

        $start = microtime(true);
        $product_info = array_merge(\Illuminate\Support\Arr::only((new ProductDataFormatter([
            'name' => $record->name,
            'url_handle' => $record->url_handle,
            'seo_title' => $record->seo_title,
            'seo_description' => $record->seo_description,
            'description' => $record->description,
            'short_description' => $record->short_description,
            'category_id' => $record->category_id,
            'vendor_id' => $record->vendor_id,
        ]))->toArray(), [
            'name', 'url_handle', 'seo_title',
            'seo_description', 'description', 'short_description',
            'category_id', 'vendor_id'
        ]), [
//            'app_import' => $record->app_import ?: static::getAppImportKey($formatter),
            'type' => $record->type,
            'tags' => $record->tags->implode('tag', ','),
            'sale' => YesNo::False,
            'xml_import_id' => $record->xml_import_id ?: $formatter->getXmlImportId(),
            'xml_import_product_id' => $record->xml_import_product_id ?: $formatter->getXmlImportProductId(),
            'xml_import_name' => $record->xml_import_name ?: $formatter->getXmlImportName(),
        ]);

        if (array_key_exists('app_import', $product_info)) {
            unset($product_info['app_import']);
        }

        if ($formatter->getUpdates()->isShipping()) {
            if ($formatter->isShipping()) {
                $product_info['require_shipping_address'] = YesNo::True;
            }
        } elseif ($record->shipping == YesNo::True) {
            $product_info['require_shipping_address'] = YesNo::True;
        }

        if ($formatter->getUpdates()->isNew()) {
            if ($formatter->isNew()) {
                if (($markAsNew = $record->getNewInterval()) && $record->new == 'no') {
                    $product_info['new_from'] = $markAsNew;
                }

                $product_info['new'] = YesNo::True;
            }
        } elseif ($record->new == YesNo::True) {
            $product_info['new'] = YesNo::True;
        }

        if ($formatter->getUpdates()->isName() && $formatter->getName()) {
            $product_info['name'] = $formatter->getName();
        }

        if ($formatter->getUpdates()->isUrlHandle() && $formatter->getUrlHandle()) {
            $product_info['url_handle'] = $formatter->getUrlHandle();
        }

        if ($formatter->getUpdates()->isShortDescription() && $formatter->getShortDescription()) {
            $product_info['short_description'] = $formatter->getShortDescription();
        }

        if ($formatter->getUpdates()->isDescription() && $formatter->getDescription()) {
            $product_info['description'] = $formatter->getDescription();
        }

        if ($formatter->getUpdates()->isSeoTitle() && $formatter->getSeoTitle()) {
            $product_info['seo_title'] = $formatter->getSeoTitle();
        }

        if ($formatter->getUpdates()->isSeoDescription() && $formatter->getSeoDescription()) {
            $product_info['seo_description'] = $formatter->getSeoDescription();
        }

        if ($formatter->getUpdates()->isTags() && $formatter->getTags()) {
            $product_info['tags'] = implode(',', array_map(fn(Tag $tag) => $tag->getTag(), $formatter->getTags()));
        }

        if ($formatter->getUpdates()->isFeatured()) {
            if ($formatter->isFeatured()) {
                $product_info['featured'] = YesNo::True;
            }
        } elseif ($record->featured == 1) {
            $product_info['featured'] = YesNo::True;
        }

        if ($formatter->getUpdates()->isTrackInventory()) {
            if ($formatter->isTracking()) {
                $product_info['track_inventory'] = YesNo::True;
                //$product_info['threshold'] = 2;
                $product_info['threshold'] = $record->threshold;
            }
        } elseif ($record->tracking == YesNo::True) {
            $product_info['track_inventory'] = YesNo::True;
            $product_info['threshold'] = $record->threshold;
        }

        if ($formatter->getUpdates()->isContinueSelling()) {
            if ($formatter->isContinueSelling()) {
                $product_info['continue_selling'] = YesNo::True;
            }
        } elseif ($record->continue_selling == YesNo::True) {
            $product_info['continue_selling'] = YesNo::True;
        }

        self::info(sprintf('End format product for %s sec', microtime(true) - $start));

        $start = microtime(true);
        if ($formatter->getUpdates()->isCategoryId() || !$record->category_id) {
            if (!empty($category_id = $formatter->getCategoryId())) {
                $product_info['category_id'] = $category_id;
            } elseif ($formatter->getCategory() && !empty($category = Category::firstOrCreate($formatter->getCategory()))) {
                $product_info['category_id'] = $category[0]->id;
            }
        }

        self::info(sprintf('End format product category for %s sec', microtime(true) - $start));

        $start = microtime(true);
        if ($formatter->getUpdates()->isVendor()) {
            if ($formatter->getVendor() && !empty($vendor = Vendor::firstOrCreate($formatter->getVendor()))) {
                $product_info['vendor_id'] = $vendor[0]->id;
            }
        } elseif ($formatter->getUpdates()->isVendorId() || !$record->vendor_id) {
            if (!empty($vendor_id = $formatter->getVendorId())) {
                $product_info['vendor_id'] = $vendor_id;
            } elseif ($formatter->getVendor() && !empty($vendor = Vendor::firstOrCreate($formatter->getVendor()))) {
                $product_info['vendor_id'] = $vendor[0]->id;
            }
        }

        self::info(sprintf('End format product vendor for %s sec', microtime(true) - $start));

        if ($formatter->getUpdates()->isMinimum() && $formatter->getMinimum() > 0) {
            $product_info['minimum'] = $formatter->getMinimum();
        }

        $variants_data = [];
        $start = microtime(true);

        if ($formatter->getUpdates()->isVariant()) {
            [$variants_info, $variants_data] = static::formatVariants($formatter, $record, true);
            $product_info = array_merge($product_info, $variants_info);
        } else {
            if (!$record->total_variants) {
                $discount = [];
                if ($formatter->getVariant() && $formatter->getVariant()->getDiscount()) {
                    $discount = $formatter->getVariant()->getDiscount()->toArray();
                }

                $formatterInstance = new ProductDataFormatter([
                    'variant' => $record->variant ? array_merge($record->variant->toArray(), $discount ? ['discount' => $discount] : []) : []
                ]);
                if ($record->variant) {
                    if (!is_null($record->variant->price)) {
                        $formatterInstance->getVariant()->setPrice($record->variant->price_input);
                    }

                    if (!is_null($record->variant->weight)) {
                        $formatterInstance->getVariant()->setWeight($record->variant->weight_input);
                    }
                }

                if (($formatterVariant = $formatter->getVariant()) && $formatterVariant->issetUpdates() && ($updates = $formatterVariant->getUpdates()) && !is_null($formatterInstance->getVariant())) {
                    if ($updates->isBarcode() && $formatterVariant->issetBarcode()) {
                        $formatterInstance->getVariant()->setBarcode($formatterVariant->getBarcode());
                    }

                    if ($updates->isSku() && $formatterVariant->issetSku()) {
                        $formatterInstance->getVariant()->setSku($formatterVariant->getSku());
                    }

                    if ($updates->isQuantity() && $formatterVariant->issetQuantity()) {
                        $formatterInstance->getVariant()->setQuantity($formatterVariant->getQuantity());
                    }

                    if ($updates->isWeight() && $formatterVariant->issetWeight()) {
                        $formatterInstance->getVariant()->setWeight($formatterVariant->getWeight());
                    }

                    if ($updates->isPrice() && $formatterVariant->issetPrice()) {
                        $formatterInstance->getVariant()->setPrice($formatterVariant->getPrice());
                    }

                    if ($updates->isMinimum() && $formatterVariant->issetMinimum()) {
                        $formatterInstance->getVariant()->setMinimum($formatterVariant->getMinimum());
                    }

                    if ($updates->isUnit() && $formatterVariant->issetUnit()) {
                        $formatterInstance->getVariant()->setUnit($formatterVariant->getUnit());
                    }

                    if ($updates->isUnitValue() && $formatterVariant->issetUnitValue()) {
                        $formatterInstance->getVariant()->setUnitValue($formatterVariant->getUnitValue());
                    }

                    if ($updates->isBaseUnit() && $formatterVariant->issetBaseUnit()) {
                        $formatterInstance->getVariant()->setBaseUnit($formatterVariant->getBaseUnit());
                    }

                    if ($updates->isBaseUnitValue() && $formatterVariant->issetBaseUnitValue()) {
                        $formatterInstance->getVariant()->setBaseUnitValue($formatterVariant->getBaseUnitValue());
                    }

                    if ($updates->isUnitText() && $formatterVariant->issetUnitText()) {
                        $formatterInstance->getVariant()->setUnitText($formatterVariant->getUnitText());
                    }

                    if ($updates->isUnitCountable() && $formatterVariant->issetUnitCountable()) {
                        $formatterInstance->getVariant()->setUnitCountable($formatterVariant->isUnitCountable());
                    }
                }
            } else {
                $formatterInstance = new ProductDataFormatter([
                    'variants' => $record->variants->map(function (VariantModel $variant): array {
                        $arr = array_merge($variant->toArray(), [
                            'unit' => $variant->unit->short_name ?? null,
                            'base_unit' => $variant->base_unit->short_name ?? null,
                        ]);
                        if (!is_null($variant->price)) {
                            $arr['price'] = $variant->price_input;
                        }

                        if (!is_null($variant->weight)) {
                            $arr['weight'] = $variant->weight_input;
                        }

                        return $arr;
                    })->all()
                ]);

                for ($i = 1; $i <= $record->total_variants; $i++) {
                    $formatterInstance->set(['p' . $i => $record->getAttribute('p' . $i)]);
                }
            }

            [$variants_info, $variants_data] = static::formatVariants($formatterInstance, $record, true);

            $product_info = array_merge($product_info, $variants_info);
        }

        self::info(sprintf('End format product variants for %s sec', microtime(true) - $start));

        $start = microtime(true);
        if (Apps::installed(GroceryStoreManager::APP_KEY)) {
            if (static::allowUpdateUnit($formatter, $product_info)) {
                static::setUnit($product_info, $formatter, $record);
            } else {
                $product_info['unit_id'] = $record->unit_id;
            }
        } else {
            $product_info['unit_id'] = null;
        }

        self::info(sprintf('End format product Grocery Store data for %s sec', microtime(true) - $start));

        $variantImages = static::exportVariantsImages($product_info);

        if ($formatter->issetIsHidden() && $formatter->getUpdates()->isHidden()) {
            static::setHidden($product_info, $formatter);
        }

        if ($formatter->getStockStatusType() && $formatter->getUpdates()->isStockStatusType()) {
            static::setStockStatus($product_info, $formatter);
        }

        $start = microtime(true);
        /** @var ProductModel $record */
        $record = static::saveDuplicateEntryCheck(function () use ($record, $product_info): \App\Models\Product\Product {
            VariantModel::resetStaticVariants();
            return $record->editModel($product_info, !$product_info['category_id'], false, false, false);
        });
        $record->load(['variant', 'variants']);
        self::info(sprintf('End save product for %s sec', microtime(true) - $start));

        $start = microtime(true);
        if ($formatter->issetStatus() && $formatter->getUpdates()->isStatus()) {
            $record->changeStatus((bool)$formatter->isStatus());
            self::info(sprintf('End update product status for %s sec. Change status for product to: %s', microtime(true) - $start, $formatter->isStatus() ? 'active' : 'inactive'));
        }

        $start = microtime(true);
        static::createIntegrationRecord($record, $formatter->getImportKey(), $formatter->getId());
        self::info(sprintf('End save product meta for integration for %s sec', microtime(true) - $start));

        $start = microtime(true);
        if (($formatter->getImage() || $formatter->getImages()) && !$record->hasImage()) {
            $imageCount = 0;
            if ($image = $formatter->getImage()) {
                if ($record->hasImage()) {
                    $record->uploadImageFromUrl($image, 'file', ['name' => $record->name]);
                } else {
                    $record->uploadImageFromUrl($image, 'file', ['name' => $record->name, 'sort_order' => $imageCount++]);
                }
            }

            if ($images = $formatter->getImages()) {
                foreach ($images as $image) {
                    if ($record->hasImage()) {
                        $record->uploadImageFromUrl($image, 'file', ['name' => $record->name]);
                    } else {
                        $record->uploadImageFromUrl($image, 'file', ['name' => $record->name, 'sort_order' => $imageCount++]);
                    }
                }
            }

            self::info(sprintf('End upload images for product for %s sec', microtime(true) - $start));
        }

        if ($formatter->getUpdates()->isCategoryId() || !$record->category_id) {
            $start = microtime(true);
            static::addCategories($record, $formatter, true);
            self::info(sprintf('End add categories for %s sec', microtime(true) - $start));
        }

        $start = microtime(true);
        static::addSuppliers($record, $formatter, true);
        self::info(sprintf('End save suppliers for product for %s sec', microtime(true) - $start));

        $start = microtime(true);
        static::setQuantitiesPerShop($record, $formatter);
        self::info(sprintf('End save quantity per shop for product for %s sec', microtime(true) - $start));

        $start = microtime(true);
        if (!empty($variants_info['variants']) && array_filter(array_column($variants_info['variants'], 'shops'))) {
            $compare = $record->variants->pluck('id', 'compare_key');
            $shops = array_map(function ($v) use ($compare) {
                if (empty($v['shops'])) {
                    return;
                }

                $v['id'] = $compare->get($v['key']);
                return $v;
            }, $variants_info['variants']);

            static::setQuantitiesPerVariantShop($record, $shops);
        }

        self::info(sprintf('End save quantity per shop for variants for %s sec', microtime(true) - $start));

        $start = microtime(true);
        static::addRedirect($record, $formatter, (int)$formatter->getUpdates()->isOldUrl());
        self::info(sprintf('End save redirects for product for %s sec', microtime(true) - $start));

        $start = microtime(true);
        static::addDiscount($record, $formatter, $variants_data, true, false);
        self::info(sprintf('End save discounts for product for %s sec', microtime(true) - $start));

        $start = microtime(true);
        static::addVariantsExternalMetaData($record, $variants_data);
        self::info(sprintf('End save variant meta for product for %s sec', microtime(true) - $start));

        $start = microtime(true);
        static::addMeta($record, $formatter);
        self::info(sprintf('End save meta for product for %s sec', microtime(true) - $start));

        $start = microtime(true);
        if ($formatter->getUpdates()->isTabs()) {
            static::addTabs($record, $formatter, true);
            self::info(sprintf('End save tabs for product for %s sec', microtime(true) - $start));
        }

        $start = microtime(true);
        if ($formatter->getUpdates()->isCategoryProperties()) {
            // add Category Properties
            static::attachCategoryProperties(false, $record, $formatter);
            self::info(sprintf('End save category properties for product for %s sec', microtime(true) - $start));
        }

        $start = microtime(true);
        if (Apps::installed('brand_model') && $formatter->issetBrandModel()) {
            if ($formatter->getUpdates()->isBrandModel() || !$record->to_brand_model()->value('id')) {
                static::addBrandModel($record, $formatter, true);
                self::info(sprintf('End save brand model for product for %s sec', microtime(true) - $start));
            }
        }

        if ($populate_temp) {
            retry(3, fn() => ProductTemp::updateTempTableAndPopulateByProduct($record->id), 300);
        }

        if (!is_null($variantImages)) {
            SiteQueue::executeQueueTaskNew([
                'job' => 'product_variants_images',
                'parameters' => [
                    'product_id' => $record->id,
                    'images' => $variantImages
                ],
            ]);
        }

        try {
            retry(3, function () use ($record): void {
                $record->touch();
            }, 1000);
        } catch (Throwable $throwable) {
            if (Str::contains($throwable->getMessage(), 'Serialization failure')) {
                return [$record, false];
            }

            throw $throwable;
        }

        return [$record, false];
    }

    /**
     * @param ProductDataFormatter $formatter
     * @param ProductModel|null $record
     * @param bool $compare
     * @return array
     */
    protected static function formatVariants(ProductDataFormatter $formatter, ?ProductModel $record = null, bool $compare = false): array
    {
        $variants_data = [];
        $product_info = [];

        $parameters = [];
        for ($i = 1; $i <= 3; $i++) {
            $product_info['p' . $i] = call_user_func([$formatter, 'getP' . $i]);
            $getParameter = ProductParameter::firstOrCreate($product_info['p' . $i]);
            $product_info['p' . $i . '_id'] = $getParameter->id ?? null;
            if ($product_info['p' . $i . '_id']) {
                $parameters[$i] = [
                    'id' => $product_info['p' . $i . '_id'],
                    'name' => $getParameter->name
                ];
            }
        }

        if ($formatter->getVariants()->count()) {
            $product_info['variants'] = [];
            foreach ($formatter->getVariants() as $variant) {
                $variant_array = $variant->toArray();
                $key = [];
                for ($i = 1; $i <= 3; $i++) {
                    if (empty($parameters[$i])) {
                        $variant_array['v' . $i] = null;
                        $variant_array['v' . $i . '_id'] = null;
                    } else {
                        $getParameterOption = ProductParameterOption::firstOrCreate($parameters[$i]['id'], $variant_array['v' . $i]);
                        $variant_array['v' . $i . '_id'] = $getParameterOption->id ?? null;
                        if ($variant_array['v' . $i . '_id']) {
                            $variant_array['v' . $i] = $getParameterOption->name;
                        }

                        $product_info['p'][$parameters[$i]['id']] = $parameters[$i];
                        $key[] = $variant_array['v' . $i . '_id'];
                    }

                    if (empty($variant_array['weight']) || !is_numeric($variant_array['weight']) || $variant_array['weight'] < 0) {
                        $variant_array['weight'] = null;
                    }
                }

                $variant_array['key'] = $variant_key = implode('_', array_filter($key));
                $product_info['variants'][] = $variant_array;
                if (!empty($discount = $variant->getDiscount())) {
                    $variants_data[$variant_key]['discount'] = $discount;
                }

                if ($variant->getExternalMetaDataKey() && $variant->getExternalMetaDataIntegration()) {
                    $variants_data[$variant_key]['external_meta_data'] = [
                        'key' => $variant->getExternalMetaDataKey(),
                        'integration' => $variant->getExternalMetaDataIntegration(),
                    ];
                }
            }

            if ($compare && $record) {
                $product_info['variants'] = static::variantsCompare($product_info['variants'], $record);
            }

            if (!empty($product_info['p'])) {
                $product_info['p'] = array_values($product_info['p']);
            }

            $product_info['type'] = ProductModel::TYPE_MULTIPLE;
        } elseif ($variant = $formatter->getVariant()) {
            //            if ($variant->getWeight() <= 0) {
            //                $formatter->setShipping(false);
            //                $variant->setWeight(null);
            //            }

            if ($variant->getWeight() <= 0) {
                $exists = !is_null($record) ? $record->variant : null;
                if ($exists && $exists->weight > 0) {
                    $variant->setWeight($exists->weight_input);
                } else {
                    $formatter->setShipping(false);
                    $variant->setWeight(null);
                }
            }

            if (is_null($variant->getPrice()) && ($exists = ($record->variant ?? null)) && is_numeric($exists->price)) {
                $variant = $variant->setPrice($exists->price_input);
            }

            $product_info['variant'] = array_merge($variant->toArray(), ['formatter' => $formatter->getVariant()]);
            if ($compare && $record && $record->default_variant_id) {
                $product_info['variant']['id'] = $record->default_variant_id;
            }

            if (!empty($discount = $variant->getDiscount())) {
                $variants_data[null]['discount'] = $discount;
            }

            if ($variant->getExternalMetaDataKey() && $variant->getExternalMetaDataIntegration()) {
                $variants_data[null]['external_meta_data'] = [
                    'key' => $variant->getExternalMetaDataKey(),
                    'integration' => $variant->getExternalMetaDataIntegration(),
                ];
            }
        }

        return [$product_info, $variants_data];
    }

    /**
     * @param ProductModel $record
     * @param ProductDataFormatter $formatter
     * @param bool $delete
     * @throws \App\Exceptions\Error
     */
    protected static function addSuppliers(ProductModel $record, ProductDataFormatter $formatter, $delete = false)
    {
        if (count($suppliers = $formatter->getSuppliers())) {
            if ($delete) {
                SupplierProducts::whereProductId($record->id)->delete();
            }

            foreach ($suppliers as $supplier_formatted) {
                if (!empty($supplier = Supplier::firstOrCreate($supplier_formatted))) {
                    if ($record->variants->count() > 0) {
                        foreach ($record->variants as $variant) {
                            $supplier->records()->firstOrCreate([
                                'product_id' => $record->id,
                                'variant_id' => $variant->id,
                                'price' => Format::toIntegerPrice($supplier_formatted->getPrice() ?: 0),
                                'price_type' => 'single',
                                'in_stock' => $supplier_formatted->isStock(),
                                'identifier' => $supplier_formatted->getSupplierCode() ?: ''
                            ]);
                        }
                    } elseif ($record->variant->exists) {
                        $variant = $record->variant;
                        $supplier->records()->firstOrCreate([
                            'product_id' => $record->id,
                            'variant_id' => $variant->id,
                            'price' => Format::toIntegerPrice($supplier_formatted->getPrice() ?: 0),
                            'price_type' => 'single',
                            'in_stock' => $supplier_formatted->isStock(),
                            'identifier' => $supplier_formatted->getSupplierCode() ?: ''
                        ]);
                    }
                }
            }
        }
    }

    /**
     * @param ProductModel $record
     * @param ProductDataFormatter $formatter
     * @param array $variants_data
     * @param bool $delete
     * @param bool $populate_temp
     * @throws Throwable
     */
    public static function addDiscount(ProductModel $record, ProductDataFormatter $formatter, array $variants_data, $delete = false, $populate_temp = true): void
    {
        $discount = null;
        if ($formatter->getDiscountId()) {
            $discount = DiscountModel::find($formatter->getDiscountId());
        }

        if (!self::mustCreateDiscount($record, $variants_data)) {
            //add global discount if has
            static::saveDuplicateEntryCheck(fn() => DiscountModel::addEditByProduct($record));

            if (empty($discount)) {
                $discount = FixedDiscount::first($formatter->getImportKey());
            }

            if ($delete && $discount) {
                ProductToDiscount::whereProductId($record->id)->whereDiscountId($discount->id)->delete();
            }

            $record->updateDefaultVariantByDiscountedPrice();

            if ($populate_temp) {
                //add global discount if has
                static::saveDuplicateEntryCheck(function () use ($record): void {
                    ProductTemp::updateTempTableAndPopulateByProduct($record->id);
                });
            }

            return;
        }

        if (empty($discount)) {
            $discount = FixedDiscount::firstOrCreate($formatter->getImportKey());
        }

        //add global discount if has
        static::saveDuplicateEntryCheck(fn() => DiscountModel::addEditByProduct($record));

        if ($delete && $discount) {
            ProductToDiscount::whereProductId($record->id)->whereDiscountId($discount->id)->delete();
        }

        if (!empty($discount) && $variants_data) {
            if (key($variants_data) == '' && ($formatted = \Illuminate\Support\Arr::first($variants_data)['discount'] ?? null) instanceof DiscountDataFormatter) {
                static::attachDiscountToVariant($record, $record->variant, $formatted, $discount);
            } elseif (key($variants_data) != '') {
                foreach ($record->variants as $variant) {
                    if (!empty($variants_data[$variant->compare_key]['discount']) && ($formatted = ($variants_data[$variant->compare_key]['discount'] ?? null)) instanceof DiscountDataFormatter) {
                        static::attachDiscountToVariant($record, $variant, $formatted, $discount);
                    }
                }
            }
        }

        $record->updateDefaultVariantByDiscountedPrice();

        if ($populate_temp) {
            //add global discount if has
            static::saveDuplicateEntryCheck(function () use ($record): void {
                ProductTemp::updateTempTableAndPopulateByProduct($record->id);
            });
        }
    }

    /**
     * @param App\Models\Product\Product $record
     * @param array $variants_data
     * @return mixed
     */
    protected static function mustCreateDiscount(ProductModel $record, array $variants_data): bool
    {
        if ($variants_data) {
            if (key($variants_data) == '' && ($formatter = \Illuminate\Support\Arr::first($variants_data)['discount'] ?? null) instanceof DiscountDataFormatter) {
                return ($record->variant ?? false) && is_numeric($formatter->getPrice()) && (float)$formatter->getPrice() < (float)$record->variant->price_input;
            } elseif (key($variants_data) != '') {
                foreach ($record->variants as $variant) {
                    if (!empty($variants_data[$variant->compare_key]['discount']) && ($formatter = ($variants_data[$variant->compare_key]['discount'] ?? null)) instanceof DiscountDataFormatter) {
                        if (is_numeric($formatter->getPrice()) && (float)$formatter->getPrice() < (float)$variant->price_input) {
                            return true;
                        }
                    }
                }
            }
        }

        return false;
    }

    /**
     * @param ProductModel $record
     * @param array $variants_data
     */
    protected static function addVariantsExternalMetaData(ProductModel $record, array $variants_data)
    {
        if ($variants_data) {
            if (key($variants_data) == '' && ($external_meta_data = \Illuminate\Support\Arr::first($variants_data)['external_meta_data'] ?? null)) {
                static::createIntegrationRecord($record->variant, $external_meta_data['integration'], $external_meta_data['key']);
            } elseif (key($variants_data) != '') {
                foreach ($record->variants as $variant) {
                    $variant_compare_key = implode('_', array_filter([$variant->v1_id, $variant->v2_id, $variant->v3_id]));
                    $external_meta_data = $variants_data[$variant_compare_key]['external_meta_data'] ?? null;
                    if ($external_meta_data) {
                        static::createIntegrationRecord($variant, $external_meta_data['integration'], $external_meta_data['key']);
                    }
                }
            }
        }
    }

    /**
     * @param ProductModel $record
     * @param ProductDataFormatter $formatter
     */
    protected static function addMeta(ProductModel $record, ProductDataFormatter $formatter)
    {
        $meta = $formatter->getMeta();
        foreach ($meta as $meta_key => $meta_value) {
            if (!is_null($meta_value)) {
                $record->meta_data()->updateOrCreate([
                    'parameter' => $meta_key,
                ], [
                    'value' => value($meta_value)
                ]);
            } else {
                $record->meta_data()->whereParameter($meta_key)->delete();
            }
        }
    }

    /**
     * @param ProductModel $record
     * @param ProductDataFormatter $formatter
     * @param bool $delete
     */
    protected static function addTabs(ProductModel $record, ProductDataFormatter $formatter, $delete = false)
    {
        if (($tabs = $formatter->getTabs()) !== []) {
            $tabsArray = [];
            foreach ($tabs as $tab) {
                if ($tab && $tab->exists) {
                    $tabsArray[] = [
                        'name' => $tab->getName(),
                        'description' => static::replaceWithVideoEmbed($tab->getDescription()),
                    ];
                }
            }

            $record->attachTabs($tabsArray);

            //            if ($delete) {
            //                $record->tabs()->delete();
            //            }
            //            foreach ($tabs AS $tab) {
            //                if ($tab && $tab->exists) {
            //                    $record->tabs()->create([
            //                        'name' => $tab->getName(),
            //                        'description' => static::replaceWithVideoEmbed($tab->getDescription()),
            //                    ]);
            //                }
            //            }
        }
    }

    /**
     * @param $content
     * @return string
     */
    protected static function replaceWithVideoEmbed($content)
    {
        if (preg_match('~^https?://(www\.)?youtube.com\/embed\/([^/]+)$~', (string) $content)) {
            return sprintf('<iframe src="%s" frameborder="0" width="100%%" height="100%%"></iframe>', $content);
        }

        return $content;
    }

    /**
     * @param ProductModel $record
     * @param ProductDataFormatter $formatter
     * @param bool $delete
     * @throws \Exception
     */
    protected static function addCategories(ProductModel $record, ProductDataFormatter $formatter, $delete = false)
    {
        /*if ($delete) {
            $record->product_to_categories()->delete();
        }*/

        $toBeAttached = [];
        // Get ids by meta data
        if ($formatter->getCategories()) {
            foreach ($formatter->getCategories() as $category) {
                if (!empty($c = Category::firstOrCreate($category))) {
                    $toBeAttached[] = $c[0]->id;
                }
            }
        }

        // Get ids by given ids
        if ($formatter->getCategoriesIds()) {
            foreach ($formatter->getCategoriesIds() as $categoryId) {
                $toBeAttached[] = $categoryId;
            }
        }

        /*foreach ($toBeAttached as $categoryId) {
            $record->product_to_categories()->firstOrCreate([
                'category_id' => $categoryId
            ]);
        }*/

        $record->attachCategories($toBeAttached);
    }

    /**
     * @param ProductDataFormatter|null $formatter
     * @return ProductModel|null
     */
    public static function findProduct(?ProductDataFormatter $formatter = null)
    {
        if ($formatter->getXmlImportId() && $formatter->getXmlImportProductId() && !(in_array($formatter->getCompareKey(), ['sku', 'barcode']) && $formatter->getCompareValue())) {
            return ProductModel::withoutGlobalScopes()
                ->whereXmlImportId($formatter->getXmlImportId())->whereXmlImportProductId($formatter->getXmlImportProductId())->firstOrNew([]);
        }

        if ($formatter->getId() && $formatter->getImportKey()) {
            $meta = ExternalMetaData::whereIn('record_type', ['product', ProductModel::class])->where('integration', $formatter->getImportKey())
                ->where('external_record_key', $formatter->getId())->first();
            if ($meta && $meta->record_id) {
                return ProductModel::withoutGlobalScopes()->findOrNew($meta->record_id);
            }
        }

        if ($formatter->getCompareKey()) {
            if (in_array($formatter->getCompareKey(), ['sku', 'barcode'])) {
                $variant = null;
                if ($formatter->getCompareValue()) {
                    $variant = VariantModel::withoutGlobalScopes()->whereNotNull($formatter->getCompareKey())->with(['item' => function ($query): void {
                        $query->withoutGlobalScopes();
                    }])->whereIn($formatter->getCompareKey(), is_array($formatter->getCompareValue()) ? $formatter->getCompareValue() : [$formatter->getCompareValue()])
                        ->first();
                }

                if (!$variant || !$variant->item) {
                    if ($formatter->getAppImport()) {
                        return ProductModel::withoutGlobalScopes()->where('app_import', $formatter->getAppImport())->firstOrNew([]);
                    }

                    return (new ProductModel())->newModelInstance();
                }

                return $variant->item;
            } elseif ($formatter->getCompareKey() == 'external_id' && $formatter->getCompareValue()) {
                return ProductModel::withoutGlobalScopes()
                    ->where('app_import', $formatter->getCompareValue())->firstOrNew([]);
            }
        }

        if (is_null($formatter) || !$formatter->exists || is_null($formatter->getName()) || $formatter->getName() == '') {
            return null;
        }

        if (
            ($app_import = static::getAppImportKey($formatter)) &&
            ($product = ProductModel::withoutGlobalScopes()->where('app_import', $app_import)->first())
        ) {
            return $product;
        }

        return (new ProductModel())->newModelInstance();
    }

    /**
     * @param $new
     * @param ProductModel $product
     * @param ProductDataFormatter $formatter
     * @throws \Exception
     */
    protected static function attachCategoryProperties($new, ProductModel $product, ProductDataFormatter $formatter)
    {
        if (!$product->category_id) {
            return;
        }

        if (!$new) {
            retry(3, function () use ($product): void {
                PropertyValue::where('product_id', $product->id)->delete();
            }, 2000);
        }

        if (!($properties = $formatter->getCategoryProperties()) || empty($product->category_id)) {
            return;
        }

        $properties_names = array_column($properties, 'name');
        $properties_names = array_filter($properties_names);
        if (!$properties_names) {
            return;
        }

        $options = [];
        $models = CategoryProperty::getAllOrCreate($properties_names);
        $option_models = CategoryPropertyOption::getAllOrCreateMultiple($properties, $models);
        foreach ($formatter->getCategoryProperties() as $property_formatted) {
            if ($models->has($key = $property_formatted->getName())) {
                /** @var Property $property */
                $property = $models->get($key);

                try {
                    PropertyCategory::firstOrCreate([
                        'property_id' => $property->id,
                        'category_id' => $product->category_id,
                    ], [
                        'property_id' => $property->id,
                        'category_id' => $product->category_id,
                    ]);
                } catch (Throwable $e) {
                    if (!str_contains($e->getMessage(), 'Duplicate')) {
                        throw $e;
                    }
                }

                if (!$property_formatted->getValues()) {
                    continue;
                }

                foreach ($property_formatted->getValues() as $value) {
                    if ($value && $option_models->has($property->id) && $option_models->get($property->id)->has($value)) {
                        /** @var PropertyOption $option */
                        $option = $option_models->get($property->id)->get($value);
                        $options[] = [
                            'option_id' => $option->id,
                            'property_id' => $property->id,
                            'product_id' => $product->id
                        ];
                    } elseif ($value && ($option = CategoryPropertyOption::firstOrCreate($property->id, $value))) {
                        $options[] = [
                            'option_id' => $option->id,
                            'property_id' => $property->id,
                            'product_id' => $product->id
                        ];
                    }
                }
            }
        }

        try {
            retry(3, function () use ($options): void {
                PropertyValue::insertUpdate($options);
            }, 2000);
        } catch (Throwable $throwable) {
            if (str_contains($throwable->getMessage(), 'constraint')) {
                return;
            } elseif (str_contains($throwable->getMessage(), 'Deadlock')) {
                PropertyValueFallback::dispatch(site('site_id'), $options)
                    ->delay(mt_rand(10, 100));
            } else {
                throw $throwable;
            }
        }
    }

    //    /**
    //     * @param $new
    //     * @param ProductModel $product
    //     * @param ProductDataFormatter $formatter
    //     * @throws \Exception
    //     */
    //    protected static function attachCategoryProperties($new, ProductModel $product, ProductDataFormatter $formatter)
    //    {
    //        if ($new) {
    //            PropertyValue::where('product_id', $product->id)->delete();
    //        }
    //        foreach ($formatter->getCategoryProperties() AS $property_formatted) {
    //            if ($property_formatted->getValues() && !empty($property = CategoryProperty::firstOrCreate($property_formatted->getName()))) {
    //                if ($product->category_id) {
    //                    $property->syncCategories($product->category_id);
    //                }
    //                foreach ($property_formatted->getValues() AS $option_name) {
    //                    if (!empty($option = CategoryPropertyOption::firstOrCreate($property->id, $option_name))) {
    //                        $option->syncProducts([
    //                            'property_id' => $property->id,
    //                            'product_id' => $product->id
    //                        ]);
    //                    }
    //                }
    //            }
    //        }
    //    }

    /**
     * @param ProductModel|null $record
     * @param VariantModel|null $variant
     * @param DiscountDataFormatter|null $formatter
     * @param DiscountModel|null $discount
     */
    protected static function attachDiscountToVariant(?ProductModel $record = null, ?VariantModel $variant = null, ?DiscountDataFormatter $formatter = null, ?DiscountModel $discount = null)
    {
        if ($record && $variant && $formatter && $discount && is_numeric($formatter->getPrice()) && (float)$formatter->getPrice() < (float)$variant->price_input) {
            DiscountModel::addFixedDiscount($discount, new ProductToDiscount([
                'product_id' => $record->id,
                'variant_id' => $variant->id,
                'price' => Format::toIntegerPrice($formatter->getPrice()),
                'save' => Format::toIntegerPrice($variant->price_input - $formatter->getPrice()),
                'date_start' => $formatter->getStartDate() ?: Carbon::now(),
                'date_end' => $formatter->getEndDate(),
            ]), false);
            //            if($res) {
            //                echo sprintf("Added discount for products \"%s\" and variant ID: %d\n", $record->name, $variant->id);
            //            } else {
            //                echo sprintf("Unable to added discount for products \"%s\" and variant ID: %d\n", $record->name, $variant->id);
            //            }
        }
    }

    /**
     * @param ProductDataFormatter $formatter
     * @return null|string
     */
    protected static function getAppImportKey(ProductDataFormatter $formatter)
    {
        if (!empty($app_import_key = $formatter->getAppImport())) {
            return $app_import_key;
        } elseif (($id = $formatter->getId()) && ($key = $formatter->getImportKey())) {
            return sprintf('%s-%s', $formatter->getImportKey(), $formatter->getId());
        }

        return;
    }

    /**
     * @param array $new_variants
     * @param ProductModel $model
     * @return array
     */
    protected static function variantsCompare(array $new_variants, ProductModel $model): array
    {
        $variants = static::variantsFormat($model);
        $totalOldVariants = $variants->count();
        $totalNewVariants = count($new_variants);
        $maxWeight = $variants->max('weight') ?: ($model->variant->weight ?? null);

        foreach ($new_variants as $row => $variant) {
            $exists = $variants->get(data_get($variant, 'key'));

            if ($exists) {
                static::populateVariantDefaults($new_variants[$row], $exists, $model);
            }

            if ($totalNewVariants === $totalOldVariants && $totalOldVariants === 1) {
                $firstVariant = $variants->first();
                static::populateFirstVariantDefaults($new_variants[$row], $firstVariant, $model);
            }

            if ($model->shipping === YesNo::True
                && (empty($new_variants[$row]['weight']) || !is_numeric($new_variants[$row]['weight']))
                && $maxWeight > 0) {
                $new_variants[$row]['weight'] = weightFloat($maxWeight);
            }
        }

        return $new_variants;
    }

    /**
     * @param array $variant
     * @param mixed $exists
     * @param App\Models\Product\Product $model
     * @return mixed
     */
    private static function populateVariantDefaults(array &$variant, $exists, ProductModel $model): void
    {
        /** @var VariantModel $exists */
        $variant['id'] = $exists->id;
        if ((empty($variant['weight']) || $variant['weight'] <= 0) && $exists->weight > 0 && $model->shipping == YesNo::True) {
            $variant['weight'] = $exists->weight_input;
        }

        if (empty($variant['unit_countable']) && $exists->unit_type) {
            $variant['unit_countable'] = $exists->unit_type == 'countable';
        }

        if (empty($variant['unit']) && $exists->unit) {
            $variant['unit'] = $exists->unit->short_name;
        }

        if (empty($variant['unit_value']) && $exists->unit_value) {
            $variant['unit_value'] = $exists->unit_value;
        }

        if (empty($variant['base_unit']) && $exists->base_unit) {
            $variant['base_unit'] = $exists->base_unit->short_name;
        }

        if (empty($variant['base_unit_value']) && $exists->base_unit_value) {
            $variant['base_unit_value'] = $exists->base_unit_value;
        }

        if (is_null($variant['price'] ?? null) && is_numeric($exists->price)) {
            $variant['price'] = $exists->price_input;
        }

        if (is_null($variant['quantity'] ?? null) && is_numeric($exists->quantity)) {
            $variant['quantity'] = $exists->quantity;
        }

        if (empty($variant['minimum']) && ($exists->minimum ?? null)) {
            $variant['minimum'] = $exists->minimum;
        }

        //        $fields = [
        //            'id' => ['property' => 'id'],
        //            'weight' => ['property' => 'weight_input', 'condition' => function ($v) use ($model) {
        //                return $v > 0 && $model->shipping === YesNo::True;
        //            }],
        //            'unit_countable' => ['property' => 'unit_type', 'condition' => function ($v) {
        //                return $v === 'countable';
        //            }],
        //            'unit' => ['property' => 'unit.short_name'],
        //            'unit_value' => ['property' => 'unit_value'],
        //            'base_unit' => ['property' => 'base_unit.short_name'],
        //            'base_unit_value' => ['property' => 'base_unit_value'],
        //            'price' => ['property' => 'price_input', 'condition' => 'is_numeric'],
        //            'quantity' => ['property' => 'quantity', 'condition' => 'is_numeric'],
        //            'minimum' => ['property' => 'minimum'],
        //        ];
        //
        //        foreach ($fields as $key => $config) {
        //            $value = data_get($exists, $config['property']);
        //            if (empty($variant[$key]) && $value) {
        //                if (isset($config['condition'])) {
        //                    $condition = $config['condition'];
        //                    if (is_callable($condition) && !$condition($value)) {
        //                        continue;
        //                    }
        //                }
        //                $variant[$key] = $value;
        //            }
        //        }
    }

    /**
     * @param array $variant
     * @param mixed $firstVariant
     * @param App\Models\Product\Product $model
     * @return mixed
     */
    private static function populateFirstVariantDefaults(array &$variant, $firstVariant, ProductModel $model): void
    {
        if (!$firstVariant) {
            return;
        }

        static::populateVariantDefaults($variant, $firstVariant, $model);
    }

    /**
     * @param ProductModel $model
     * @return Collection
     */
    protected static function variantsFormat(ProductModel $model)
    {
        /** @var Collection $variants */
        $variants = $model->variants->map(function (VariantModel $variant) use ($model): ?\App\Models\Product\Variant {
            $key = [];
            for ($i = 1; $i <= $model->total_variants; $i++) {
                $key[] = $variant['v' . $i . '_id'];
            }

            $key = implode('_', array_filter($key));
            if (empty($key)) {
                return null;
            }

            $variant->setAttribute('key', $key);
            return $variant;
        })->filter();
        return $variants->keyBy('key');
    }

    /**
     * @param ProductModel $product
     * @param ProductDataFormatter $formatter
     */
    protected static function setQuantitiesPerShop($product, $formatter)
    {
        if (AppsManager::isInstalled(Stores::APP_KEY)) {
            Quantity::whereProductId($product->id)->delete();
            if ($formatter->getStoresQuantity()) {
                foreach ($formatter->getStoresQuantity() as $shopTitle => $quantity) {
                    $shop = Shop::firstOrCreate(new ShopFormatter(['title' => $shopTitle]));

                    Quantity::create([
                        'shop_id' => $shop->id,
                        'product_id' => $product->id,
                        'variant_id' => $product->default_variant_id,
                        'qty' => $quantity
                    ]);
                }
            }
        }
    }

    /**
     * @param ProductModel $product
     * @param array $variants
     */
    protected static function setQuantitiesPerVariantShop($product, $variants)
    {
        if (AppsManager::isInstalled(Stores::APP_KEY)) {
            if (is_array($variants)) {
                foreach ($variants as $variant) {
                    $variant_id = $variant['id'] ?? null;
                    if ($variant_id && !empty($variant['shops']) && is_array($variant['shops'])) {
                        foreach ($variant['shops'] as $shopData) {
                            $shop = Shop::firstOrCreate(new ShopFormatter(['title' => $shopData['title']]));

                            if ($shop->wasRecentlyCreated) {
                                $shop->update(['active' => 1]);
                            }

                            Quantity::updateOrCreate([
                                'shop_id' => $shop->id,
                                'product_id' => $product->id,
                                'variant_id' => $variant_id,
                            ], [
                                'qty' => $shopData['quantity_in_shop']
                            ]);
                        }
                    }
                }
            } else {
                Quantity::whereProductId($product->id)->get()->map->delete();
            }
        }
    }

    /**
     * @param ProductModel $record
     * @param ProductDataFormatter $formatter
     * @param bool $delete
     * @throws \Exception
     */
    protected static function addBrandModel(ProductModel $record, ProductDataFormatter $formatter, $delete = false)
    {
        if ($formatter->getBrandModel()) {
            if ($delete) {
                $record->to_brand_model()->delete();
            }

            foreach ($formatter->getBrandModel() as $brand) {
                if (!empty($brand['model']) && !empty($brand['name']) && !empty($b = Brand::firstOrCreate($brand['name']))) {
                    $models = array_map(fn($model) => Model::firstOrCreate($b->id, $model)->id ?? null, is_array($brand['model']) ? $brand['model'] : [$brand['model']]);
                    $models = array_filter($models);
                    $inserts = [];
                    foreach ($models as $model_id) {
                        $inserts[] = [
                            'product_id' => $record->id,
                            'brand_id' => $b->id,
                            'model_id' => $model_id,
                        ];
                    }

                    if ($inserts) {
                        $record->to_brand_model()->insert($inserts);
                    }
                }
            }
        }
    }

    /**
     * @param $product_info
     * @param ProductDataFormatter $formatter
     */
    protected static function setStockStatus(array &$product_info, $formatter)
    {
        if ($formatter->getStockStatusType()) {
            $status = Status::whereType($formatter->getStockStatusType())->first();

            if (!$status) {
                $status = Status::create([
                    'type' => $formatter->getStockStatusType(),
                    'name' => 'System status',
                    'quantity_operator_id' => 1,
                    'quantity' => 0
                ]);
            }

            $product_info['status_id'] = $product_info['out_of_stock_id'] = $status->id;
        }
    }

    /**
     * @param $product_info
     * @param ProductDataFormatter $formatter
     */
    protected static function setHidden(array &$product_info, $formatter)
    {
        if ($formatter->issetIsHidden()) {
            $product_info['is_hidden'] = $formatter->getIsHidden();
        }
    }

    /**
     * @param ProductDataFormatter $formatter
     * @param $product_info
     * @return bool
     */
    protected static function allowUpdateUnit(ProductDataFormatter $formatter, $product_info): bool
    {
        $updates = $formatter->getUpdates();
        return $updates->isUnit() ||
            $updates->isBaseUnit() ||
            $updates->isUnitValue() ||
            $updates->isBaseUnitValue() ||
            $updates->isUnitCountable() ||
            $updates->isVariant();
    }

    /**
     * @param bool $hasGroceryStore
     * @param array $variant
     * @param ProductModel|null $record
     * @return array
     */
    protected static function setUnitToVariant(bool $hasGroceryStore, array $variant, ?ProductModel $record = null): array
    {
        $unsetAndReturn = function (bool $toNull = false) use ($variant) {
            foreach (['unit_countable', 'unit', 'unit_value', 'base_unit', 'base_unit_value', 'unit_text'] as $key) {
                if ($toNull) {
                    $variant[$key] = null;
                } else {
                    unset($variant[$key]);
                }
            }

            return array_merge($variant, [
                'unit_type' => 'measured',
            ]);
        };
        if (!$hasGroceryStore || empty($variant['unit']) || empty($variant['unit_value']) || !is_numeric($variant['unit_value'])) {
            return $unsetAndReturn();
        }

        $unitType = (function (array $variant) use ($record): string {
            if (!$record || ($variant['updates']['unit_countable'] ?? false) || !!array_key_exists('unit_countable', $variant)) {
                return $variant['unit_countable'] ? 'countable' : 'measured';
            }

            return $variant['unit_countable'] ? 'countable' : 'measured';
        })($variant);

        $unitId = (function (array $variant) use ($record) {
            if ((!$record || ($variant['updates']['unit'] ?? false)) && array_key_exists('unit', $variant)) {
                return $variant['unit'] ? Units::where('short_name', $variant['unit'])->value('id') : null;
            }

            return !empty($variant['unit']) ? Units::where('short_name', $variant['unit'])->value('id') : null;
        })($variant);

        if (!$unitId) {
            return $unsetAndReturn();
        }

        $unitValue = (function (array $variant) use ($record) {
            if ((!$record || ($variant['updates']['unit_value'] ?? false)) && array_key_exists('unit_value', $variant)) {
                if ($variant['unit_value'] > 0) {
                    return ($variant['unit_value']);
                }
            }

            if (empty($variant['unit_value']) || $variant['unit_value'] <= 0 || !is_numeric($variant['unit_value'])) {
                return;
            }

            return $variant['unit_value'];
        })($variant);

        $unitText = (function (array $variant) use ($record) {
            if ((!$record || ($variant['updates']['unit_text'] ?? false)) && array_key_exists('unit_text', $variant)) {
                if (!empty($variant['unit_text'])) {
                    return ($variant['unit_text']);
                }
            }

            if (empty($variant['unit_text']) || !is_scalar($variant['unit_text'])) {
                return;
            }

            return $variant['unit_text'];
        })($variant);

        $baseUnitId = null;
        $unitBaseValue = 1;
        if ($unitType == 'countable') {
            $baseUnitId = (function (array $variant) use ($record) {
                if ((!$record || ($variant['updates']['base_unit'] ?? false)) && array_key_exists('base_unit', $variant)) {
                    return $variant['base_unit'] ? Units::where('short_name', $variant['base_unit'])->value('id') : null;
                }

                return !empty($variant['base_unit']) ? Units::where('short_name', $variant['base_unit'])->value('id') : null;
            })($variant);

            if (!$baseUnitId) {
                return $unsetAndReturn();
            }

            $unitBaseValue = (function (array $variant) use ($record) {
                if ((!$record || ($variant['updates']['base_unit_value'] ?? false)) && array_key_exists('base_unit_value', $variant)) {
                    if ($variant['base_unit_value'] > 0) {
                        return $variant['base_unit_value'];
                    }
                }

                if (empty($variant['base_unit_value']) || $variant['base_unit_value'] <= 0 || !is_numeric($variant['base_unit_value'])) {
                    return;
                }

                return $variant['base_unit_value'];
            })($variant);
        }

        return array_merge($unsetAndReturn(), [
            'unit_type' => $unitType,
            'unit_id' => $unitId,
            'unit_value' => $unitValue,
            'base_unit_id' => $baseUnitId,
            'base_unit_value' => $unitBaseValue,
            'unit_text' => $unitText,
        ]);
    }

    /**
     * @param $product_info
     * @param ProductDataFormatter $formatter
     * @param ProductModel|null $record
     */
    protected static function setUnit(&$product_info, ProductDataFormatter $formatter, ?ProductModel $record = null)
    {
        $hasGroceryStore = Apps::installed(GroceryStoreManager::APP_KEY);
        if (!empty($product_info['variants'])) {
            $product_info['variants'] = array_map(fn($variant): array => static::setUnitToVariant($hasGroceryStore, $variant, $record), $product_info['variants']);
        } elseif (!empty($product_info['variant'])) {
            $product_info['variant'] = Arr::first(array_map(fn($variant): array => static::setUnitToVariant($hasGroceryStore, $variant, $record), [$product_info['variant']]));
        }
    }

    //    /**
    //     * @param $product_info
    //     * @param ProductDataFormatter $formatter
    //     * @param ProductModel $record
    //     */
    //    protected static function setUnit(&$product_info, $formatter, $record = null)
    //    {
    //        $product_info['unit_id'] = $record->unit_id ?? null;
    //        if (Apps::installed(GroceryStoreManager::APP_KEY) && !empty($formatter->getUnit())) {
    //            $product_info['unit_id'] = Units::where('short_name', $formatter->getUnit())->value('id');
    //        }
    //
    //        if (!$product_info['unit_id']) {
    //            if (!empty($product_info['variants'])) {
    //                $product_info['variants'] = array_map(function ($variant) {
    //                    return array_merge($variant, [
    //                        'unit_type' => 'measured',
    //                        'unit_id' => null,
    //                        'unit_value' => null,
    //                        'base_unit' => null,
    //                        'base_unit_value' => null,
    //                        'unit_text' => null,
    //                    ]);
    //                }, $product_info['variants']);
    //            } elseif (!empty($product_info['variant'])) {
    //                $product_info['variant'] = array_merge($product_info['variant'], [
    //                    'unit_type' => 'measured',
    //                    'unit_id' => null,
    //                    'unit_value' => null,
    //                    'base_unit' => null,
    //                    'base_unit_value' => null,
    //                    'unit_text' => null,
    //                ]);
    //            }
    //        } else {
    //            if (!empty($product_info['variants'])) {
    //                $product_info['variants'] = array_map(function ($variant) use ($product_info, $formatter) {
    //                    $variant['unit_id'] = $product_info['unit_id'];
    //                    $variant['unit_value'] = $variant['unit_value'] > 0 ? $variant['unit_value'] : $formatter->getUnitValue();
    //                    $variant['unit_text'] = empty($variant['unit_text']) ? $formatter->getUnitText() : $variant['unit_text'];
    //                    return $variant;
    //                }, $product_info['variants']);
    //            } elseif (!empty($product_info['variant'])) {
    //                $product_info['variant']['unit_id'] = $product_info['unit_id'];
    //                if ($formatter->getUpdates()->isUnitValue() && ($unit_value = $formatter->getVariant()->getUnitValue() ?: $formatter->getUnitValue()) > 0) {
    //                    $product_info['variant']['unit_value'] = $unit_value;
    //                } else {
    //                    $product_info['variant']['unit_value'] = $product_info['variant']['unit_value'] > 0 ? $product_info['variant']['unit_value'] : ($formatter->getVariant()->getUnitValue() ?: $formatter->getUnitValue());
    //                }
    //                if ($formatter->getUpdates()->isUnitText() && ($unit_text = $formatter->getVariant()->getUnitText() ?: $formatter->getUnitText())) {
    //                    $product_info['variant']['unit_text'] = $unit_text;
    //                } else {
    //                    $product_info['variant']['unit_text'] = !empty($product_info['variant']['unit_text']) ? $product_info['variant']['unit_text'] : ($formatter->getVariant()->getUnitText() ?: $formatter->getUnitText());
    //                }
    //            }
    //        }
    //    }

    /**
     * @param $product_info
     * @return null|array
     */
    protected static function exportVariantsImages(&$product_info): ?array
    {
        return null;
        $variants_images = null;
        if ($product_info['type'] == 'multiple' && !empty($product_info['variants'])) {
            foreach ($product_info['variants'] as $r => $v) {
                foreach ($v['image_urls'] as $img) {
                    if (!array_key_exists($img, is_array($variants_images) ? $variants_images : [])) {
                        $variants_images[$img] = [
                            'image' => $img,
                            'keys' => []
                        ];
                    }

                    $variants_images[$img]['keys'][] = $v['key'];
                }

                unset($product_info['variants'][$r]['image_urls']);
            }
        }

        return is_array($variants_images) ? array_values($variants_images) : null;
    }

    /**
     * @param \Modules\Importer\Jobs\Importer|null $cli
     * @return mixed
     */
    public static function setCli(?Importer $cli): void
    {
        self::$cli = $cli;
    }

    /**
     * @param string $message
     * @return mixed
     */
    public static function info(string $message): void
    {
        if (self::$cli) {
            self::$cli->info($message);
        }
    }

    /**
     * @param string $message
     * @return mixed
     */
    public static function warn(string $message): void
    {
        if (self::$cli) {
            self::$cli->warn($message);
        }
    }

    /**
     * @param string $message
     * @return mixed
     */
    public static function error(string $message): void
    {
        if (self::$cli) {
            self::$cli->error2($message);
        }
    }
}
