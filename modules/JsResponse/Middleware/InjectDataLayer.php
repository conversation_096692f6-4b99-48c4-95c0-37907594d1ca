<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 31.5.2019 г.
 * Time: 13:46 ч.
 */

namespace Modules\JsResponse\Middleware;

use App\Exceptions\Error;
use App\Helper\Catalog\Pages;
use App\Helper\Catalog\Products;
use App\Helper\Format;
use App\Helper\Registry;
use App\Helper\Store\CartTotal;
use App\Helper\Store\Contracts\AddressContract;
use App\Helper\Store\Contracts\ItemContract;
use App\Helper\Store\Contracts\OrderContract;
use App\Helper\Widgets\WidgetCall;
use App\Helper\YesNo;
use App\Models\Customer\Customer;
use App\Models\Layout\FormFields;
use App\Models\Order\Order;
use App\Models\Order\OrderPayment;
use App\Models\Page\Page;
use App\Models\Product\Category;
use App\Models\Product\Product;
use App\Models\Product\Variant;
use App\Models\Product\Vendor;
use App\Models\Router\Logs;
use App\Models\Store\Cart as CartModel;
use App\Traits\ResponseType;
use Auth;
use Closure;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Arr;
use lib\widget\blog\Article;
use lib\widget\blog\Blog;
use Throwable;

class InjectDataLayer
{
    use ResponseType;

    /**
     * @var null|Order
     */
    protected $order;

    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);

        if ($this->shouldModifyResponse($response)) {
            return $this->modifyResponse($response, $request);
        }

        return $response;
    }

    /**
     * @param $response
     * @return bool
     */
    protected function shouldModifyResponse($response): bool
    {
        return
            app_namespace() == 'site' &&
            $response instanceof Response &&
            $response->isOk();
    }

    /**
     * @param Response $response
     * @param Request $request
     * @return Response
     */
    protected function modifyResponse(Response $response, Request $request): Response
    {
        switch (true) {
            case activeRoute('product.view'):
                $this->__productView($response, $request);
                break;
            case activeRoute('ajax.search'):
            case activeRoute('products.search'):
                $this->__productSearch($response, $request);
                break;
            case activeRoute('category.view'):
            case activeRoute('ajax.category'):
                $this->__categoryView($response, $request);
                break;
            case activeRoute('site.vendor.view'):
            case activeRoute('ajax.vendor'):
                $this->__vendorView($response, $request);
                break;
            case activeRoute('site.vendors'):
                $this->__vendorsList($response, $request);
                break;
            case activeRoute('page'):
                $this->__pageView($response, $request);
                break;
            case activeRoute('contacts'):
                $this->__pageContacts($response, $request);
                break;
            case activeRoute('blog.list blog.view'):
                $this->__blogList($response, $request);
                break;
            case activeRoute('blog.article.view'):
                $this->__pageArticle($response, $request);
                break;
            case activeRoute('site.home'):
                $this->__pageHome($response, $request);
                break;
            case activeRoute('cart.list'):
                $this->__pageCart($response, $request);
                break;
            case activeRoute('checkout') || $request->is(['checkout/login', 'checkout/register', 'checkout/shipping-address']):
                $this->__pageCheckout($response, $request);
                break;
            case activeRoute('checkout.return'):
                $this->__pageCheckoutReturn($response, $request);
                break;
            case !$request->ajax() && $this->getResponseType($response) == 'html':
                $this->__pageDefault($response, $request);
                break;
        }

        return $response;
    }

    /****************************************** replaces ******************************************/
    /**
     * @param Response $response
     * @param Request $request
     * @return Response
     */
    protected function __productView(Response $response, Request $request): \Illuminate\Http\Response
    {
        $data = [];
        try {
            $data = $this->getProductData($this->_getProduct(), true);
        } catch (Exception) {
        }

        return $this->__replaceContent($data, $response, $request, 'body');
    }

    /**
     * @param Response $response
     * @param Request $request
     * @return Response
     */
    protected function __productSearch(Response $response, Request $request): \Illuminate\Http\Response
    {
        $data = [];
        try {
            $data = array_merge($this->getProductsData(), [
                'query_string' => is_scalar($query = request()->get('query')) ? $query : null,
                'type' => 'search'
            ]);
        } catch (Exception) {
        }

        return $this->__replaceContent($data, $response, $request);
    }

    /**
     * @param Response $response
     * @param Request $request
     * @return Response
     */
    protected function __categoryView(Response $response, Request $request): \Illuminate\Http\Response
    {
        $data = [];
        try {
            if ($category = $this->_getCategory()) {
                $data = [
                    'type' => 'category',
                    'name' => $category->name,
                    'url' => $category->url,
                    'breadcrumb' => $category->path->map(fn(Category $category): array => [
                        'name' => $category->name,
                        'url' => $category->url,
                    ])->all(),
                    'products' => $this->getProductsData()['products'] ?? []
                ];
            }
        } catch (Exception) {
        }

        return $this->__replaceContent($data, $response, $request);
    }

    /**
     * @param Response $response
     * @param Request $request
     * @return Response
     */
    protected function __vendorView(Response $response, Request $request): \Illuminate\Http\Response
    {
        $data = [];
        try {
            $data = $this->getVendorData($this->_getVendor(), true);
        } catch (Exception) {
        }

        return $this->__replaceContent($data, $response, $request);
    }

    /**
     * @param Response $response
     * @param Request $request
     * @return Response
     */
    protected function __pageView(Response $response, Request $request): \Illuminate\Http\Response
    {
        $data = [];
        try {
            $data = $this->getPageData();
        } catch (Exception) {
        }

        return $this->__replaceContent($data, $response, $request);
    }

    /**
     * @param Response $response
     * @param Request $request
     * @return Response
     */
    protected function __pageContacts(Response $response, Request $request): \Illuminate\Http\Response
    {
        $data = [
            'type' => 'contacts',
        ];

        return $this->__replaceContent($data, $response, $request);
    }

    /**
     * @param Response $response
     * @param Request $request
     * @return Response
     */
    protected function __blogList(Response $response, Request $request): \Illuminate\Http\Response
    {
        $data = [];
        try {
            $blog = $this->_getBlog();
            $data = [
                'type' => 'blog',
                'blogs' => $blog ? $this->getBlogsData($blog->getBlogs()) : null,
                'articles' => $blog ? $this->getArticlesData($blog->getArticles()) : null,
            ];
        } catch (Exception) {
        }

        return $this->__replaceContent($data, $response, $request);
    }

    /**
     * @param Response $response
     * @param Request $request
     * @return Response
     */
    protected function __pageArticle(Response $response, Request $request): \Illuminate\Http\Response
    {
        $data = [];
        try {
            $data = $this->getBlogArticleData();
        } catch (Exception) {
        }

        return $this->__replaceContent($data, $response, $request);
    }

    /**
     * @param Response $response
     * @param Request $request
     * @return Response
     */
    protected function __pageDefault(Response $response, Request $request): \Illuminate\Http\Response
    {
        $data = [
            'type' => 'other',
        ];

        return $this->__replaceContent($data, $response, $request);
    }

    /**
     * @param Response $response
     * @param Request $request
     * @return Response
     */
    protected function __pageHome(Response $response, Request $request): \Illuminate\Http\Response
    {
        $data = [
            'type' => 'home',
        ];

        return $this->__replaceContent($data, $response, $request);
    }

    /**
     * @param Response $response
     * @param Request $request
     * @return Response
     */
    protected function __pageCart(Response $response, Request $request): \Illuminate\Http\Response
    {
        $data = [];
        try {
            $data = $this->getCartData();
        } catch (Throwable) {
        }

        return $this->__replaceContent($data, $response, $request);
    }

    /**
     * @param Response $response
     * @param Request $request
     * @return Response
     */
    protected function __pageCheckout(Response $response, Request $request): \Illuminate\Http\Response
    {
        $data = [];
        try {
            $data = $this->getCartData('checkout');
        } catch (Throwable $throwable) {
            Logs::createFromThrowable($throwable, 'DataLayerJs Response');
        }

        return $this->__replaceContent($data, $response, $request);
    }

    /**
     * @param Response $response
     * @param Request $request
     * @return Response
     */
    protected function __pageCheckoutReturn(Response $response, Request $request): \Illuminate\Http\Response
    {
        $data = [];
        try {
            $data = $this->getCheckoutData();
        } catch (Throwable $throwable) {
            Logs::createFromThrowable($throwable, 'DataLayerJs Response');
        }

        return $this->__replaceContent($data, $response, $request);
    }

    /**
     * @param Response $response
     * @param Request $request
     * @return Response
     */
    protected function __vendorsList(Response $response, Request $request): \Illuminate\Http\Response
    {
        $data = [];
        try {
            $data = $this->getVendorsData();
        } catch (Exception) {
        }

        return $this->__replaceContent($data, $response, $request);
    }

    /**
     * @param array $data
     * @param Response $response
     * @param Request $request
     * @param string $ajaxKey
     * @return Response
     */
    protected function __replaceContent(array $data, Response $response, Request $request, string $ajaxKey = 'html'): Response
    {
        $content = $response->getOriginalContent();
        if (isLiquidEngine()) {
            $response->setContent($content);

            return $response;
        }
        if(is_array($content)) {
            return $response;
        }

        $data['iid'] = site('industry');

        $replace = sprintf(
            "<script type=\"text/javascript\" id='js-cc-page-data'>\nvar cc_page_data = %s;\ndataLayer.push({cc_page_data: cc_page_data});\n</script>",
            json_encode($data, JSON_UNESCAPED_UNICODE /*| JSON_PRETTY_PRINT*/)
        );

        if ($request->ajax()) {
            if (is_array($content)) {
                if (array_key_exists($ajaxKey, $content) && is_string($content[$ajaxKey])) {
                    $content[$ajaxKey] = $this->__substrReplace($replace, $content[$ajaxKey]);
                }
            } else {
                $content = $this->__substrReplace($replace, $content);
            }
        } else {
            $content = str_replace([
                '</title>',
            ], [
                sprintf("</title>\n%s", $replace),
            ], (string) $content);
        }

        $response->setContent($content);

        return $response;
    }

    protected function __substrReplace(?string $replace, ?string $content): string
    {
//        return preg_replace('/<\/div>/', sprintf("%s\n</div>", $replace), (string) $content, 1);
        $content = (string) $content;
        $pos = strrpos($content, '</div>');
        if ($pos !== false) {
            $content = substr_replace($content, $replace . "\n</div>", $pos, strlen('</div>'));
        }

        return $content;
    }

    /****************************************** formatters ******************************************/

    /**
     * @return array
     * @throws Throwable
     */
    protected function getCheckoutData(): array
    {
        if (routeParameter('status') == 'cancel') {
            return $this->getCartData('checkout');
        }

        /** @var OrderPayment $order_payment */
        $order_payment = with(Registry::get('checkout.order_payment'), fn($orderPayment): mixed => $orderPayment ? clone $orderPayment : null);
        if (!$order_payment) {
            return [];
        }

        $this->order = $order_payment->order;

        $shipping = $this->order->getShipping();
        $billingAddress = $this->order->getBillingAddress();
        $shippingAddress = $this->order->getShippingAddress();
        $products = $this->order->getProducts();

        $products_data = [];
        foreach ($products as $product) {
            $products_data[] = $this->getCartProductData($product, $this->order);
        }

        $discounts = $this->order->getTotalsSimple()
            ->filter(fn($key): bool => str_contains((string) $key->key, 'discount.'));

        $discountsFormatted = $discounts->map(function (CartTotal $total) {
            if (!is_numeric($total->price_input)) {
                return;
            }

            $data = Arr::only($total->toArray(), [
                'name', 'description', 'currency'
            ]);

            $data['price'] = number_format($total->price_input * -1, 2, '.', '');

            return $data;
        })->filter()->values();

        $totals = $this->order->getTotalsSimple()->map(function (CartTotal $total) {
            $data = Arr::only($total->toArray(), [
                'name', 'description', 'currency', 'value'
            ]);

            $data['key'] = explode('.', (string) $total->key)[0];
            $data['price'] = $total->price_input;

            return $data;
        })->values();

        return [
            'type' => 'checkout',
            'id' => $this->order->id,
            'status' => $this->order->status,
            'status_formatted' => $this->order->status_formatted,
            'fulfillment_status' => $this->order->status_fulfillment,
            'fulfillment_status_formatted' => $this->order->status_fulfillment_formatted,
            'subtotal' => $this->order->getSubTotal('input'),
            'discount' => number_format($discounts->sum('price_input') * -1, 2, '.', ''),
            'total' => $this->order->getTotal('input'),
            'currency' => $this->order->currency ?? site('currency'),
            'locale' => $this->order->locale ?? site('language'),
            'products_count' => $products->count(),
            'products' => $products_data,
            'discounts' => $discountsFormatted->toArray(),
            'chosen_shipping' => !is_null($shipping) ? array_merge(['name' => $shipping->name, 'price' => $shipping->price_input], $this->getAddressData($shippingAddress, 'shipping_address')) : [],
            'shipping_addresses' => $this->getAddressesData('shipping_address'),
            'chosen_billing_address' => $this->getAddressData($billingAddress, 'billing_address'),
            'billing_addresses' => $this->getAddressesData('billing_address'),
            'taxes' => $this->getTaxesData(),
            'payment' => [
                "provider" => $order_payment->provider,
                "status" => $order_payment->status,
                "status_formatted" => $order_payment->status_formatted,
                "provider_name" => $order_payment->provider_name,
            ],
            'created_at' => $this->order->date_added->toIso8601String(),
            'vat_included' => $this->order->hasVatIncluded(),
            'totals' => $totals->toArray(),
            'url' => route('checkout.return', ['status' => $this->order->status, 'payment_hash' => $order_payment->hash]),
            'customer' => $this->order->customer ? $this->getCustomerData($this->order->customer, $this->order->customer->isGuest() ? 'guest' : 'customer') : null
        ];
    }

    /**
     * @param string $type
     * @return array
     * @throws Throwable
     */
    protected function getCartData(string $type = 'cart'): array
    {
        if (!($cartInstance = CartModel::instance())) {
            return [];
        }

        $shipping = $cartInstance->getShipping();
        $billingAddress = $cartInstance->getBillingAddress();
        $shippingAddress = $cartInstance->getShippingAddress();

        $products_data = [];
        foreach ($cartInstance->products as $product) {
            $products_data[] = $this->getCartProductData($product, $cartInstance);
        }

        $discounts_total_price = $cartInstance->getTotalsSimple()
            ->filter(fn($key): bool => str_contains((string) $key->key, 'discount.'))
            ->sum('price_input');

        return [
            'type' => $type,
            'id' => $cartInstance->id,
            'subtotal' => $cartInstance->getSubTotal('input'),
            'discount' => $discounts_total_price,
            'total' => $cartInstance->getTotal('input'),
            'currency' => site('currency'),
            'products_count' => $cartInstance->products->count(),
            'products' => $products_data,
            'discounts' => $this->getDiscountsData(),
            'chosen_shipping' => !is_null($shipping) ? array_merge(['name' => $shipping->name], $this->getAddressData($shippingAddress, 'shipping_address')) : [],
            'shipping_addresses' => $this->getAddressesData('shipping_address'),
            'chosen_billing_address' => $this->getAddressData($billingAddress, 'billing_address'),
            'billing_addresses' => $this->getAddressesData('billing_address'),
            'taxes' => $this->getTaxesData(),
            'url' => $type == 'checkout' ? route('checkout') : route('cart.list', ['cart_key' => $cartInstance->key]),
            'customer' => $cartInstance->customer ? $this->getCustomerData($cartInstance->customer, $cartInstance->customer->isGuest() ? 'guest' : 'customer') : null
        ];
    }

    /**
     * @param Customer $customer
     * @param string $type
     * @return array
     */
    protected function getCustomerData(Customer $customer, string $type): array
    {
        $customFields = [];

        if ($customer->custom_data->isNotEmpty()) {
            $fields = FormFields::getByForm('register')->keyBy('id');
            foreach ($customer->custom_data as $data) {
                if (!$fields->has($data->field_id)) {
                    continue;
                }

                /** @var FormFields $field */
                $field = $fields->get($data->field_id);
                if ($field->allow_options) {
                    if ($field->multiple && is_array($data->value)) {
                        $customFields[$field->name] = $field->options->whereIn('id', $data->value)->pluck('name')->values()->all();
                    } elseif (is_numeric($data->value) && ($option = $field->options->firstWhere('id', $data->value))) {
                        $customFields[$field->name] = $option->name;
                    }
                } else {
                    $customFields[$field->name] = $data->value;
                }
            }
        }

        return array_merge([
            'id' => $customer->id,
            'group_id' => $customer->group_id,
            'group_name' => $customer->group->name ?? null,
            'first_name' => $customer->first_name,
            'last_name' => $customer->last_name,
            'full_name' => $customer->full_name,
            'email' => $customer->email,
            'type' => $type,
            'cpadm' => (int)!!Auth::adminId()
        ], $customFields);
    }

    /**
     * @return array
     * @throws Throwable
     */
    protected function getTaxesData(): array
    {
        $taxes = ($checkout = $this->getInstance()) ? $checkout->getNotVatTaxes() : null;

        if ($taxes && !$taxes->isEmpty()) {
            foreach ($taxes as $tax) {
                return
                    [
                        'type' => 'tax',
                        'id' => $tax->id,
                        'name' => !empty($tax->name) ? $tax->name : '',
                        'tax_type' => !empty($tax->type) ? $tax->type : '',
                        'value' => $tax->tax_formatted,
                    ];
            }
        }

        return [];
    }

    /**
     * @param $type
     * @return array
     * @throws Throwable
     */
    protected function getAddressesData(/** @noinspection PhpUnusedParameterInspection */ $type): array
    {
        //        $addresses = [];
        //        switch ($type) {
        //            case 'shipping_address' :
        //                $addresses = ($checkout = $this->getInstance()) ? $checkout->getShippingAddresses() : null;
        //                break;
        //            case 'billing_address' :
        //                $addresses = ($checkout = $this->getInstance()) ? $checkout->getBillingAddresses() : null;
        //                break;
        //        }
        //
        //        $addresses_data = [];
        //        if ($addresses && !$addresses->isEmpty()) {
        //            foreach ($addresses as $address) {
        //                $addresses_data[] = $this->getAddressData($address, $type);
        //            }
        //        }

        return [];//$addresses_data;
    }

    /**
     * @param AddressContract|null $address
     * @param $type
     * @return array
     */
    protected function getAddressData(?AddressContract $address, $type): array
    {
        if ($address !== null) {
            return [
                'type' => $type,
                'id' => $address->getKey(),
                'address' => $address->getText(),
                'post_code' => $address->getPostCode(),
                'phone' => $address->getPhone(),
            ];
        }

        return [];
    }

    /**
     * @return CartModel|bool
     * @throws Throwable
     */
    protected function getCart()
    {
        if (app_namespace() != 'site') {
            return;
        }

        return CartModel::instance();
    }

    /**
     * @return null|OrderContract
     * @throws Throwable
     */
    protected function getInstance()
    {
        return $this->order ?: $this->getCart();
    }

    /**
     * @return array
     * @throws Throwable
     */
    protected function getDiscountsData(): array
    {
        $discounts = ($checkout = $this->getInstance()) ? $checkout->getDiscountsOver() : null;
        if ($discounts && !$discounts->isEmpty()) {
            foreach ($discounts as $discount) {
                return
                    [
                        'type' => 'discount',
                        'id' => $discount->id,
                        'name' => !empty($discount->name) ? $discount->name : '',
                        'discount_type' => !empty($discount->type) ? $discount->type : '',
                        'date_start' => !empty($discount->date_start) ? Format::datetime($discount->date_start) : '',
                        'date_end' => !empty($discount->date_end) ? Format::datetime($discount->date_end) : '',
                        'active' => $discount->active == YesNo::True,
                        'value' => $discount->getTypeValueFormattedAttribute(),
                    ];
            }
        }

        return [];
    }

    /**
     * @param ItemContract $item
     * @param OrderContract $order
     * @return array
     */
    protected function getCartProductData(ItemContract $item, OrderContract $order): array
    {
        $itemArray = $item->toArray();

        return array_merge($itemArray, [
            'type' => 'product',
            'id' => $item->getId(),
            'parameter_id' => $item->parameter_id,
            'name' => $item->getName(),
            'category' => $item->getCategoryName(),
            'category_path' => $item->getCategoryPath(),
            'sku' => $item->getSku(),
            'barcode' => $item->getBarcode(),
            'brand' => $item->getVendorName(),
            'price' => moneyInput(Arr::get($itemArray, 'total_price.price', 0), $order->getCurrency()),
//            'discount_price' => !is_null($item->discount_total_input) ? $item->discount_total_input : '',
            'single_price' => moneyInput(Arr::get($itemArray, 'total_price_per_item.price', 0), $order->getCurrency()),
//            'discount_single_price' => !is_null($item->discount_price_input) ? $item->discount_price_input : '',
            'quantity' => $item->getQuantity(),
            'currency' => $order->getCurrency(),
            'digital' => $item->isDigital(),
            'availability' => !empty($item->product->active) && $item->product->active == YesNo::True ? 'active' : 'inactive',
            'url' => $item->url,
            'image_url' => $item->getImage(),
            'parameters' => $item->getParameters(),
        ]);
    }

    /**
     * @param null|Collection $articles
     * @return array
     */
    protected function getArticlesData(?Collection $articles = null): array
    {
        if ($articles !== null) {
            $data = [];
            foreach ($articles as $article) {
                $data[] = [
                    'type' => 'article',
                    'id' => $article->id,
                    'blog_id' => $article->blog_id,
                    'name' => $article->name,
                    'url' => $article->url(),
                ];
            }

            return $data;
        }

        return [];
    }

    /**
     * @return array
     */
    protected function getBlogArticleData(): array
    {

        try {
            $article = $this->_getArticle()
                ->getArticle(routeParameter('slug'));

            return [
                'type' => 'article',
                'id' => $article->id,
                'blog_id' => $article->blog_id,
                'name' => $article->name,
                'url' => $article->url(),
            ];
        } catch (Exception) {
            return [];
        }
    }

    /**
     * @param array $blogs
     * @return array
     */
    protected function getBlogsData(array $blogs): array
    {
        if ($blogs) {
            $data = [];
            foreach ($blogs as $blog) {
                $data[] = [
                    'type' => 'blog',
                    'id' => $blog->id,
                    'name' => $blog->name,
                    'url' => $blog->url(),
                ];
            }

            return $data;
        }

        return [];
    }

    /**
     * @return array
     */
    protected function getPageData(): array
    {
        if (!is_null($page = $this->_getPage())) {
            return [
                'type' => 'page',
                'id' => $page->id,
                'name' => $page->name,
                'url' => $page->url(),
            ];
        }

        return [];
    }

    /**
     * @return array
     * @throws Error
     */
    protected function getVendorsData(): array
    {
        $vendors = Vendor::where('name', '<>', '')
            ->orderBy('name')->get();
        if ($vendors && !$vendors->isEmpty()) {
            $vendors_data = [];
            foreach ($vendors as $vendor) {
                $vendors_data[] = $this->getVendorData($vendor);
            }

            return $vendors_data;
        }

        return [];
    }

    /**
     * @param null|Vendor $vendor
     * @param bool $with_products
     * @return array
     * @throws Error
     */
    protected function getVendorData(?Vendor $vendor = null, bool $with_products = false): array
    {
        if ($vendor === null) {
            return [];
        }

        $result = [
            'type' => 'vendor',
            'id' => $vendor->id,
            'name' => $vendor->name,
            'url' => $vendor->url,
        ];
        if ($with_products) {
            $result['products'] = $this->getProductsData()['products'] ?? [];
        }

        return $result;
    }

    /**
     * @param Product|null $product
     * @param bool $product_is_from_details_page
     * @return array
     * @throws Error
     */
    protected function getProductData(?Product $product = null, bool $product_is_from_details_page = false): array
    {
        if ($product === null) {
            return [];
        }

        $data = [
            'type' => 'product',
            'id' => $product->id,
            'parameter_id' => $product->parameter_id ?? $product->id,
            'name' => $product->name,
            'category' => isset($product->category) ? $product->category->name : '',
            'category_path' => $product->category ? $product->category->path->implode('name', ' > ') : null,
            'brand' => $product->vendor->name ?? '',
            'brand_image' => $product->vendor && $product->vendor->hasImage() ? $product->vendor->getImage('150x150') : null,
            'sku' => $product->variant->sku ?? '',
            'barcode' => $product->variant->barcode ?? '',
            'digital' => $product->digital,
            'url' => $product->url,
            'has_image' => $product->hasImage(),
            'image_url' => $product->getImage(),
            'product_type' => $product->type,
            'total_parameters' => $product->total_variants,
        ];

        if ($product->total_variants > 0) {
            if (!$product_is_from_details_page) {
                $vids = Arr::only($product->getAttributes(), ['v1_id', 'v2_id', 'v3_id']);
                if (array_filter($vids)) {
                    $data['parameter_id'] = implode('_', array_merge([$data['id']], array_back_empty_trim($vids)));
                }
            } /*else {
                if ($vids = request()->only(['v1', 'v2', 'v3'])) {
                    if (array_filter($vids)) {
                        $data['id'] = implode('_', array_merge([$data['id']], array_back_empty_trim($vids)));
                    }
                } elseif (($parameters = Parameter::getParametersForListing()) && $parameters->isNotEmpty()) {
                    $vids = [];
                    for ($i = 1; $i <= $product->total_variants; $i++) {
                        if ($parameters->has($product->getAttribute('p' . $i . '_id'))) {
                            $vids[] = $product->variant->getAttribute('v' . $i . '_id');
                        } else {
                            $vids[] = 0;
                        }
                    }
                    if (array_filter($vids)) {
                        $data['id'] = implode('_', array_merge([$data['id']], array_back_empty_trim($vids)));
                    }
                }
            }*/
        }

        if (showPriceForUser()) {
            $data = array_merge($data, [
                'price' => is_numeric($product->price_from_input) ? floatval($product->price_from_input) : $product->price_from_input,
                'discount_price' => is_numeric($product->price_from_discounted_input ?? '') ? floatval($product->price_from_discounted_input ?? '') : ($product->price_from_discounted_input ?? ''),
                'currency' => $product->currency_code,
            ]);
        }

        if ($product_is_from_details_page) {
            $variants = $product->variants;
            if ($variants && !$variants->isEmpty()) {
                $variants->transform(function (Variant $variant) use ($product): \App\Models\Product\Variant {
                    $variant->setRelation('item', clone $product);
                    return $variant;
                });

                $data['variants'] = $this->getVariantsData($variants);
            }
        }

        return $data;
    }

    /**
     * @param $variants
     * @return array
     */
    protected function getVariantsData($variants): array
    {
        $variants_data = [];
        foreach ($variants as $variant) {
            $first_params = $this->getParamsFormatted($variant->p1, $variant->v1);
            $second_params = $this->getParamsFormatted($variant->p2, $variant->v2);
            $third_params = $this->getParamsFormatted($variant->p3, $variant->v3);

            $variant_data = [
                'id' => $variant->id,
                'parameters' => array_filter([$first_params, $second_params, $third_params]),
                'availability' => $variant->status_type,
            ];

            if (showPriceForUser()) {
                $variant_data = array_merge($variant_data, [
                    'price' => $variant->price_input,
                    'discount_price' => $variant->price_discounted_input,
                ]);
            }

            $variants_data[] = $variant_data;
        }

        return $variants_data;
    }

    /**
     * @param mixed $name
     * @param mixed $value
     * @return mixed
     */
    protected function getParamsFormatted($name, $value): ?array
    {
        if (!is_null($name) && !is_null($value)) {
            return [
                'param_name' => $name,
                'param_value' => $value
            ];
        }

        return null;
    }

    /****************************************** get records ******************************************/

    /**
     * @return array
     * @throws Error
     */
    protected function getProductsData(): array
    {
        $products = $this->_getProducts();
        if ($products && !$products->isEmpty()) {
            $products_data = [];
            foreach ($products as $product) {
                $products_data[] = $this->getProductData($product);
            }

            return [
                'type' => 'products_list',
                'products' => $products_data,
            ];
        }

        return [];
    }

    /**
     * @return null|Product
     */
    protected function _getProduct()
    {
        try {
            return Products::getCurrently();
        } catch (Exception) {
            return;
        }
    }

    /**
     * @return null|Collection
     */
    protected function _getProducts()
    {
        try {
            return WidgetCall::productsListing()->getProducts();
        } catch (Exception) {
            return;
        }
    }

    /**
     * @return null|Category
     */
    protected function _getCategory()
    {
        try {
            return WidgetCall::filters()->getCategory();
        } catch (Exception) {
            return;
        }
    }

    /**
     * @return null|Vendor
     */
    protected function _getVendor()
    {
        try {
            return WidgetCall::filters()->getVendor();
        } catch (Exception) {
            return;
        }
    }

    /**
     * @return null|Page
     */
    protected function _getPage()
    {
        try {
            return Pages::getCurrently();
        } catch (Exception) {
        }

        return;
    }


    /**
     * @return Blog|null
     */
    protected function _getBlog()
    {
        try {
            return WidgetCall::blog();
        } catch (Exception) {
        }

        return;
    }


    /**
     * @return Article|null
     */
    protected function _getArticle()
    {
        try {
            return WidgetCall::article();
        } catch (Exception) {
        }

        return;
    }
}
