<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 14.3.2019 г.
 * Time: 16:53 ч.
 */

namespace Modules\DatalayerEvents\Events;

use App\Events\OrderProductModificationRemove;
use App\Events\PostOrderStatusChange;
use Auth;
use App\Events\FulfillmentAdd;
use App\Events\OrderProductDiscountAdd;
use App\Events\OrderProductDiscountRemove;
use App\Models\Queue\SiteQueue;
use Illuminate\Events\Dispatcher;
use App\Events\FulfillmentRemove;
use App\Events\OrderArchiveToggle;
use App\Events\OrderProductAdd;
use App\Events\OrderProductEdit;
use App\Events\OrderProductRemove;
use App\Events\OrderShippingChange;
use App\Events\PaymentSync;
use App\Models\Order\Order;
use Modules\DatalayerEvents\Jobs\OrderUpdate;
use Modules\DatalayerEvents\Formatters\OrderFormatter;
use Modules\DatalayerEvents\Formatters\CustomerFormatter;
use Throwable;

/**
 * @deprecated
 */
class OrderEventSubscriber
{
    /**
     * @param \App\Events\PaymentSync $event
     * @throws Throwable
     */
    public function onPaymentSync(PaymentSync $event): void
    {
        $this->createEvent($event->payment->order);
    }

    /**
     * @param \App\Events\OrderProductAdd $event
     * @throws Throwable
     */
    public function onProductAdd(OrderProductAdd $event): void
    {
        $this->createEvent($event->order_product->order);
    }

    /**
     * @param \App\Events\OrderProductEdit $event
     * @throws Throwable
     */
    public function onProductEdit(OrderProductEdit $event): void
    {
        $this->createEvent($event->order_product->order);
    }

    /**
     * @param \App\Events\OrderProductRemove $event
     * @throws Throwable
     */
    public function onProductRemove(OrderProductRemove $event): void
    {
        $this->createEvent($event->order_product->order);
    }

    /**
     * @param \App\Events\PostOrderStatusChange $event
     * @throws Throwable
     */
    public function onStatusChange(PostOrderStatusChange $event): void
    {
        $this->createEvent($event->order);
    }

    /**
     * @param \App\Events\FulfillmentAdd $event
     * @throws Throwable
     */
    public function onFulfillmentAdd(FulfillmentAdd $event): void
    {
        $this->createEvent($event->fulfillment->order);
    }

    /**
     * @param \App\Events\FulfillmentRemove $event
     * @throws Throwable
     */
    public function onFulfillmentRemove(FulfillmentRemove $event): void
    {
        if ($f = $event->fulfillments->first()) {
            $this->createEvent($f->order);
        }
    }

    /**
     * @param \App\Events\OrderArchiveToggle $event
     * @return mixed
     */
    public function onArchiveToggle(OrderArchiveToggle $event): void
    {
        $this->createEvent($event->order);
    }

    /**
     * @param \App\Events\OrderShippingChange $event
     * @return mixed
     */
    public function onShippingChange(OrderShippingChange $event): void
    {
        $this->createEvent($event->new_shipping->order);
    }

    /**
     * @param \App\Events\OrderProductDiscountRemove $event
     * @return mixed
     */
    public function onOrderProductDiscountRemove(OrderProductDiscountRemove $event): void
    {
        $this->createEvent($event->order);
    }

    /**
     * @param \App\Events\OrderProductModificationRemove $event
     * @return mixed
     */
    public function onOrderProductModificationRemove(OrderProductModificationRemove $event): void
    {
        $this->createEvent($event->order);
    }

    /**
     * @param \App\Events\OrderProductDiscountAdd $event
     * @return mixed
     */
    public function onOrderProductDiscountAdd(OrderProductDiscountAdd $event): void
    {
        $this->createEvent($event->order);
    }

    /**
     * Register the listeners for the subscriber.
     *
     * @param Dispatcher $events
     */
    public function subscribe($events): void
    {
        return;

        $events->listen(
            \App\Events\PaymentSync::class,
            $this->onPaymentSync(...)
        );

        $events->listen(
            \App\Events\OrderProductAdd::class,
            $this->onProductAdd(...)
        );

        $events->listen(
            \App\Events\OrderProductEdit::class,
            $this->onProductEdit(...)
        );

        $events->listen(
            \App\Events\OrderProductRemove::class,
            $this->onProductRemove(...)
        );

        $events->listen(
            \App\Events\PostOrderStatusChange::class,
            $this->onStatusChange(...)
        );

        $events->listen(
            \App\Events\FulfillmentRemove::class,
            $this->onFulfillmentRemove(...)
        );

        $events->listen(
            \App\Events\FulfillmentAdd::class,
            $this->onFulfillmentAdd(...)
        );

        $events->listen(
            \App\Events\OrderArchiveToggle::class,
            $this->onArchiveToggle(...)
        );

        $events->listen(
            \App\Events\OrderShippingChange::class,
            $this->onShippingChange(...)
        );

        $events->listen(
            \App\Events\OrderProductDiscountRemove::class,
            $this->onOrderProductDiscountRemove(...)
        );

        $events->listen(
            \App\Events\OrderProductModificationRemove::class,
            $this->onOrderProductModificationRemove(...)
        );

        $events->listen(
            \App\Events\OrderProductDiscountAdd::class,
            $this->onOrderProductDiscountAdd(...)
        );
    }

    /**
     * @param Order $order
     * @throws Throwable
     */
    protected function createEvent(Order $order)
    {
        if (!config('uuid.enabled')) {
            return;
        }

        SiteQueue::executeQueueTask(OrderUpdate::class, [
            'event' => 'PurchaseUpdate',
            'site_id' => site('site_id'),
            'external_id' => (int)$order->id,
            'uuid_id' => app()->runningInConsole() ? 'console' : sprintf('admin-%d', Auth::adminId()),
            'order' => OrderFormatter::format($order),
            'customer' => CustomerFormatter::format($order->customer),
        ]);
    }
}
