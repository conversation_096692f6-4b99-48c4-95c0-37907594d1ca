[{"$match": {"site_id": {"$in": "SITE_IDS"}, "date": {"$gte": "DATE_GTE", "$lte": "DATE_LTE"}, "$or": [{"status": {"$in": ["paid", "completed", "authorized"]}}, {"status": "pending", "status_fulfillment": "not_fulfilled"}, {"status_fulfillment": "fulfilled"}], "customer_id": {"$ne": null}}}, {"$group": {"_id": {"_id": "$site_id"}, "site_id": {"$first": "$site_id"}, "date": {"$first": "$date"}, "customers": {"$addToSet": {"$cond": {"if": {"$ne": ["$customer_id", null]}, "then": "$customer_id", "else": "$$REMOVE"}}}}}, {"$addFields": {"customers": {"$size": "$customers"}}}, {"$group": {"_id": null, "self": {"$sum": {"$cond": {"if": {"$eq": ["$site_id", "SITE_ID"]}, "then": "$customers", "else": 0}}}, "other": {"$sum": {"$cond": {"if": {"$ne": ["$site_id", "SITE_ID"]}, "then": "$customers", "else": 0}}}, "sites": {"$addToSet": "$site_id"}}}, {"$addFields": {"sites": {"$subtract": [{"$size": "$sites"}, 1]}}}, {"$addFields": {"avg": {"$ceil": {"$cond": {"if": {"$gt": ["$sites", 0]}, "then": {"$divide": ["$other", "$sites"]}, "else": 0}}}}}, {"$addFields": {"type": {"$cond": {"if": {"$gt": ["$self", "$avg"]}, "then": "above", "else": "below"}}, "percent": {"$cond": {"if": {"$gt": ["$self", "$avg"]}, "then": {"$cond": {"if": {"$gt": ["$avg", 0]}, "then": {"$round": [{"$multiply": [{"$divide": ["$self", "$avg"]}, 100]}, 2]}, "else": {"$cond": {"if": {"$gt": ["$self", 0]}, "then": 100, "else": 0}}}}, "else": {"$cond": {"if": {"$gt": ["$avg", 0]}, "then": {"$round": [{"$cond": {"if": {"$gt": ["$self", 0]}, "then": {"$subtract": [100, {"$multiply": [{"$divide": ["$self", "$avg"]}, 100]}]}, "else": 100}}, 2]}, "else": 0}}}}}}, {"$addFields": {"period": "PERIOD", "full_total": {"$add": ["$self", "$other"]}, "sites_with_self": {"$add": ["$sites", 1]}}}, {"$addFields": {"_id": {"$concat": [{"$convert": {"input": "SITE_ID", "to": "string"}}, "-", "AGGREGATION", "-", "$period"]}, "site_id": "SITE_ID", "date": "DATE", "aggregation": "AGGREGATION"}}]