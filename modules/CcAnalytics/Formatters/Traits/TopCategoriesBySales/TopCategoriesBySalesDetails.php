<?php

declare(strict_types=1);

namespace Modules\CcAnalytics\Formatters\Traits\TopCategoriesBySales;

use Carbon\Carbon;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Modules\CcAnalytics\Constants;
use Modules\CcAnalytics\Models\CcAnalyticsCategories;
use Modules\CcAnalytics\Models\CcAnalyticsOrders;
use Modules\CcAnalytics\Models\CcAnalyticsProducts;

trait TopCategoriesBySalesDetails
{
    /**
     * @inerhitDoc
     */
    protected function periodDetailsCompare(): array
    {
        $currently = $this->getDetailsForDates(
            $this->interval[0]->clone()->startOfDay(),
            $this->interval[1]->clone()->endOfDay(),
            null,
            $this->isExport() ? null : Constants::DETAILS_PAGINATION_LIMIT
        );

        $previouss = $currently->isNotEmpty() ? $this->getDetailsForDates(
            $this->interval[2]->clone()->startOfDay(),
            $this->interval[3]->clone()->endOfDay(),
            $currently->pluck('id')->all(),
            $this->isExport() ? null : Constants::DETAILS_PAGINATION_LIMIT
        ) : collect();

        $previouss = $previouss->keyBy(function (CcAnalyticsOrders $brand): string {
            $brand->conversion_rate = get_percentage($brand->views, $brand->sales);
            return sprintf('%d_%s', $brand->id, md5($brand->name));
        });

        $currently->transform(function (CcAnalyticsOrders $current) use ($previouss): array {
            $previous = $previouss->get(sprintf('%d_%s', $current->id, md5($current->name)));
            return [
                'page_name' => $current->name,
                'views' => $current->views,
                'orders' => $current->sales,
                'quantity' => $current->quantity,
                'amount' => $current->amount,
                'unit' => $current->unit ?? $previous->unit ?? null,
                'conversion_rate' => $current->conversion_rate,
                'page_viewMore' => $current->viewMore,
                'page_url' => $current->url,
                'page_name_previous' => $previous->name ?? $current->name,
                'views_previous' => $previous->views ?? 0,
                'orders_previous' => $previous->sales ?? 0,
                'quantity_previous' => $previous->quantity ?? 0,
                'amount_previous' => $previous->amount ?? 0,
                'conversion_rate_previous' => $previous->conversion_rate ?? 0,
                'page_viewMore_previous' => $previous->viewMore ?? null,
                'page_url_previous' => $previous->url ?? $current->url,
                'unit_previous' => $previous->unit ?? $current->unit,
            ];
        });

        return collect([
            'table' => $currently,
            'headers' => ['page_name', 'views', 'orders', 'quantity', 'conversion_rate', 'amount',],
        ])->toArray();
    }

    /**
     * @inerhitDoc
     */
    protected function noDetailsCompare(): array
    {
        $records = $this->getDetailsForDates(
            $this->interval[0]->clone()->startOfDay(),
            $this->interval[1]->clone()->endOfDay(),
            null,
            $this->isExport() ? null : Constants::DETAILS_PAGINATION_LIMIT
        );

        $records->transform(fn(CcAnalyticsOrders $current): array => [
            'page_name' => $current->name,
            'views' => $current->views,
            'orders' => $current->sales,
            'quantity' => $current->quantity,
            'amount' => $current->amount,
            'conversion_rate' => $current->conversion_rate,
            'page_viewMore' => $current->viewMore,
            'page_url' => $current->url,
            'unit' => $current->unit,
        ]);

        return collect([
            'table' => $records,
            'headers' => ['page_name', 'views', 'orders', 'quantity', 'conversion_rate', 'amount',],
        ])->toArray();
    }

    /**
     * @param Carbon $from
     * @param Carbon $to
     * @param array|null $ids
     * @param int|null $limit
     * @return LengthAwarePaginator|Collection
     */
    protected function getDetailsForDates(Carbon $from, Carbon $to, ?array $ids = [], ?int $limit = Constants::DETAILS_PAGINATION_LIMIT): \Illuminate\Pagination\LengthAwarePaginator|\Illuminate\Support\Collection
    {
        if ($this->isExport()) {
            $results = CcAnalyticsOrders::getTopCategoriesBySalesDetailsExport($from, $to, $ids);
        } else {
            $results = CcAnalyticsOrders::getTopCategoriesBySalesDetails($from, $to, $ids, $limit);
        }

        if ($results->isEmpty()) {
            return $results;
        }

        $sessions = CcAnalyticsCategories::getTopCategoriesByTrafficDetailsExport($from, $to, $results->pluck('id')->all())
            ->pluck('unique', 'category_id');

        //$sessions2 = CcAnalyticsProducts::getTopCategoriesFromProductsDetails($from, $to, $results->pluck('id')->all());

        $results->transform(function (CcAnalyticsOrders $category) use ($sessions): \Modules\CcAnalytics\Models\CcAnalyticsOrders {
            $category->views = $sessions->get($category->id, 0);// + $sessions2->get($category->id, 0);
            $category->conversion_rate = get_percentage($category->views, $category->sales);
            return $category;
        });

        return $results;
    }
}
