<?php

declare(strict_types=1);

namespace Modules\CcAnalytics\Responses\Jobs;

use Modules\CcAnalytics\Helpers\ReferrerHostParse;
use MongoDB\BSON\ObjectId;

class TrafficSourcePerDayResponse extends AbstractResponse
{
    /** @var string $referrer_host */
    protected $referrer_host;

    /** @var string|null $referrer_name */
    protected $referrer_name;

    /** @var string|null $referrer_group */
    protected $referrer_group;

    /** @var string $source */
    protected $source;

    /** @var string|null $device */
    protected $device;

    /** @var string|null $utm_source */
    protected $utm_source;

    /** @var string|null $utm_medium */
    protected $utm_medium;

    /** @var string|null $utm_campaign */
    protected $utm_campaign;

    /** @var string $uuid_id */
    protected $uuid_id;

    /** @var string|null $record_type */
    protected $record_type;

    /** @var int|null $record_id */
    protected $record_id;

    /** @var string|null $record_name */
    protected $record_name;

    /** @var string|null $record_url */
    protected $record_url;

    /** @var string|null $record_search */
    protected $record_search;

    /** @var string|null $country */
    protected $country;

    /** @var string|null $city */
    protected $city;

    /**
     * @param array $data
     * @param int $site_id
     * @return mixed
     */
    public function __construct(array $data, int $site_id)
    {
        parent::__construct($data, $site_id);

        $this->guessReferrerData($data);
    }

    /**
     * @return string|null
     */
    public function getReferrerHost(): ?string
    {
        return $this->referrer_host;
    }

    /**
     * @param string|null $referrer_host
     */
    public function setReferrerHost(?string $referrer_host): void
    {
        $this->referrer_host = $referrer_host;
    }

    /**
     * @return string|null
     */
    public function getSource(): ?string
    {
        return $this->source;
    }

    /**
     * @param string|null $source
     */
    public function setSource(?string $source): void
    {
        $this->source = $source;
    }

    /**
     * @return string|null
     */
    public function getDevice(): ?string
    {
        return $this->device;
    }

    /**
     * @param string|null $device
     */
    public function setDevice(?string $device): void
    {
        $this->device = $device;
    }

    /**
     * @return string|null
     */
    public function getUtmSource(): ?string
    {
        return $this->utm_source;
    }

    /**
     * @param string|null $utm_source
     */
    public function setUtmSource(?string $utm_source): void
    {
        $this->utm_source = $utm_source;
    }

    /**
     * @return string|null
     */
    public function getUtmMedium(): ?string
    {
        return $this->utm_medium;
    }

    /**
     * @param string|null $utm_medium
     */
    public function setUtmMedium(?string $utm_medium): void
    {
        $this->utm_medium = $utm_medium;
    }

    /**
     * @return string|null
     */
    public function getUtmCampaign(): ?string
    {
        return $this->utm_campaign;
    }

    /**
     * @param string|null $utm_campaign
     */
    public function setUtmCampaign(?string $utm_campaign): void
    {
        $this->utm_campaign = $utm_campaign;
    }

    /**
     * @return string|null
     */
    public function getReferrerName(): ?string
    {
        return $this->referrer_name;
    }

    /**
     * @param string|null $referrer_name
     */
    public function setReferrerName(?string $referrer_name): void
    {
        $this->referrer_name = $referrer_name;
    }

    /**
     * @return string|null
     */
    public function getReferrerGroup(): ?string
    {
        return $this->referrer_group ?: 'unknown';
    }

    /**
     * @param string|null $referrer_group
     */
    public function setReferrerGroup(?string $referrer_group): void
    {
        $this->referrer_group = $referrer_group;
    }

    #[\Override]
    public function getId(): string
    {
        return sprintf(
            '%s-%s-%s-%s-%s-%s-%s-%s',
            parent::getId(),
            md5((string) $this->getReferrerHost()),
            $this->getSource(),
            $this->getDevice(),
            $this->getUtmSource() ?: '-',
            $this->getUtmMedium() ?: '-',
            $this->getUtmCampaign() ?: '-',
            $this->getUuidId()
        );
    }

    /**
     * @param array $data
     * @return mixed
     */
    protected function guessReferrerData(array $data)
    {
        if ($this->getSource() == 'direct') {
            $this->setReferrerGroup('unknown');
            $this->setReferrerName('Direct');
        } elseif ($this->getReferrerHost()) {
            $this->setReferrerName($this->getReferrerHost());
        }

        if (!empty($data['referrer_url'])) {
            $parser = new ReferrerHostParse($data['referrer_url']);
            $parsed = $parser->parse();
            if ($parsed) {
                $this->setReferrerGroup($parsed['group']);
                $this->setReferrerName($parsed['name']);
            }
        }
    }

    /**
     * @return string
     */
    public function getUuidId(): string
    {
        return strval($this->uuid_id);
    }

    /**
     * @param mixed $uuid_id
     */
    public function setUuidId($uuid_id): void
    {
        if ($uuid_id instanceof ObjectId) {
            $uuid_id = strval($uuid_id);
        }

        $this->uuid_id = $uuid_id;
    }

    /**
     * @return string|null
     */
    public function getRecordType(): ?string
    {
        return $this->record_type;
    }

    /**
     * @return int|null
     */
    public function getRecordId(): ?int
    {
        return $this->record_id;
    }

    /**
     * @return string|null
     */
    public function getRecordName(): ?string
    {
        return is_null($this->record_name) ? null : (string) $this->record_name;
    }

    /**
     * @return string|null
     */
    public function getRecordUrl(): ?string
    {
        return $this->record_url;
    }

    /**
     * @return string|null
     */
    public function getRecordSearch(): ?string
    {
        return is_null($this->record_search) ? null : (string) $this->record_search;
    }

    /**
     * @param int|null $record_id
     * @return TrafficSourcePerDayResponse
     */
    public function setRecordId(?int $record_id): TrafficSourcePerDayResponse
    {
        $this->record_id = $record_id;
        return $this;
    }

    /**
     * @param mixed $record_name
     * @return TrafficSourcePerDayResponse
     */
    public function setRecordName(mixed $record_name): TrafficSourcePerDayResponse
    {
        $this->record_name = $record_name;
        return $this;
    }

    /**
     * @param string|null $record_url
     * @return TrafficSourcePerDayResponse
     */
    public function setRecordUrl(?string $record_url): TrafficSourcePerDayResponse
    {
        $this->record_url = $record_url;
        return $this;
    }

    /**
     * @param mixed $record_search
     * @return TrafficSourcePerDayResponse
     */
    public function setRecordSearch(mixed $record_search): TrafficSourcePerDayResponse
    {
        $this->record_search = $record_search;
        return $this;
    }

    /**
     * @param string|null $record_type
     * @return TrafficSourcePerDayResponse
     */
    public function setRecordType(?string $record_type): TrafficSourcePerDayResponse
    {
        $this->record_type = $record_type;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getCountry(): ?string
    {
        return $this->country;
    }

    /**
     * @param string|null $country
     */
    public function setCountry(?string $country): void
    {
        $this->country = $country;
    }

    /**
     * @return string|null
     */
    public function getCity(): ?string
    {
        return is_null($this->city) ? null : (string) $this->city;
    }

    /**
     * @param mixed $city
     */
    public function setCity(mixed $city): void
    {
        $this->city = $city;
    }

    /**
     * @inerhitDoc
     */
    #[\Override]
    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'referrer_host' => $this->getReferrerHost(),
            'referrer_name' => $this->getReferrerName(),
            'referrer_group' => $this->getReferrerGroup(),
            'source' => $this->getSource(),
            'device' => $this->getDevice(),
            'utm_source' => $this->getUtmSource(),
            'utm_medium' => $this->getUtmMedium(),
            'utm_campaign' => $this->getUtmCampaign(),
            'uuid_id' => $this->getUuidId(),
            'country' => $this->getCountry(),
            'city' => $this->getCity(),
            'record_type' => $this->getRecordType(),
            'record_id' => $this->getRecordId(),
            'record_name' => $this->getRecordName(),
            'record_url' => $this->getRecordUrl(),
            'record_search' => $this->getRecordSearch(),
            'utm_filter' => $this->getUtmSource() || $this->getUtmMedium() ? implode(' / ', [
                $this->getUtmSource() ?: '--',
                $this->getUtmMedium() ?: '--',
            ]) : null,
            'utm_filter_full' => $this->getUtmSource() || $this->getUtmMedium() ? implode(' / ', [
                $this->getUtmSource() ?: '--',
                $this->getUtmMedium() ?: '--',
                $this->getUtmCampaign() ?: '--',
            ]) : null,
            'referrer_key' => implode('-', [
                $this->getReferrerGroup() ?: '--',
                $this->getReferrerName() ?: '--',
            ]),
        ]);
    }

}
