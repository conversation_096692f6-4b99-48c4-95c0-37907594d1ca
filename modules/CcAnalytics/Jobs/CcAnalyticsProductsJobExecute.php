<?php

declare(strict_types=1);

namespace Modules\CcAnalytics\Jobs;

use CcAnalytics;
use Exception;
use Modules\CcAnalytics\Constants;
use Modules\CcAnalytics\Helpers\UtcDate;
use Modules\CcAnalytics\Models\CcAnalyticsStatistic;
use Modules\CcAnalytics\Responses\Jobs\ProductsPerDayResponse;
use MongoDB\BSON\Regex;

class CcAnalyticsProductsJobExecute extends AbstractCcAnalyticsExecute
{
    /**
     * @return void
     * @throws Exception
     */
    public function handle(): void
    {
        if(in_array($this->site_id, config('uuid.disabled_sites', []))) {
            $this->warn('Site is disabled for analytics data!');
            return;
        }

        $this->info(sprintf('Start queue job for site #%d', $this->site_id));

        $startTime = $this->findLastExecuteDate($this->site_id, 'productsPerDay');
        if (is_null($startTime)) {
            $this->error(sprintf('There is no start date for site #%d!', $this->site_id));
            return;
        }

        if ($startTime->clone()->addHours($this->getExecutionTimeInHours()) > now('UTC')) {
            $this->warn(sprintf('Start time %s for records is not less from now %s', $startTime->clone()->addHours($this->getExecutionTimeInHours())->toIso8601String(), now('UTC')->toIso8601String()));
            CcAnalyticsStatistic::updatePipelineExecution($this->site_id, 'productsPerDay', now('UTC'), $this->getExecutionTimeInHours());
            return;
        }

        $this->info(sprintf('Start time for records is %s', $startTime->toIso8601String()));

        $endTime = $startTime->clone()->addHours($this->getExecutionTimeInHours());

        $pipline = CcAnalytics::getPipeline('productsPerDay');
        $aggregation = $this->eventsAggregate($pipline, [
            'SITE_ID' => $this->site_id,
            'DATE_GTE' => UtcDate::make($startTime),
            'DATE_LT' => UtcDate::make($endTime),
            'UUID' => [
                '$not' => new Regex('/^admin-.*/i')
            ],
        ]);

        $this->info('Get matched records from mongodb!');

        $r = 0;
        foreach ($aggregation as $a) {
            $visitors = ProductsPerDayResponse::make($a, $this->site_id);
            $this->getCollection(Constants::COLLECTION_PRODUCTS)
                ->updateOne(['_id' => $visitors->getId()], [
                    '$set' => $visitors->toArray()
                ], [
                    'upsert' => true
                ]);
            $r++;
        }

        $this->info(sprintf('End store %d records!', $r));

        $this->info(sprintf('Begin update pipeline record with date %s!', ($endTime ?? now('UTC'))->toIso8601String()));
        CcAnalyticsStatistic::updatePipelineExecution($this->site_id, 'productsPerDay', $endTime, $this->getExecutionTimeInHours());

        if (($endTime ?? now('UTC')) < now('UTC')) {
            $this->warn('Release job!');
            $this->release();
        } else {
            $this->info('Done');
        }
    }

}
