<?php

declare(strict_types=1);

namespace Modules\CcAnalytics\Traits\Models\Products\TopProductsByTraffic;

use Carbon\Carbon;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Collection as IlluminateCollection;
use Modules\CcAnalytics\Constants;
use Modules\CcAnalytics\Formatters\AbstractFormatter;
use Modules\CcAnalytics\Helpers\ForceLengthAwarePaginator;
use MongoDB\Collection as MongoCollection;
use MongoDB\Collection;
use MongoDB\Laravel\Connection;

trait TopProductsByTrafficViewMore
{
    /**
     * @param Carbon $from
     * @param Carbon $to
     * @param int $id
     * @param int|null $limit
     * @return LengthAwarePaginator
     */
    public static function getTopProductsByTrafficViewMore(Carbon $from, Carbon $to, int $id, int $limit = Constants::DETAILS_PAGINATION_LIMIT): LengthAwarePaginator
    {
        $total = static::getTopProductsByTrafficViewMoreCount($from, $to, $id);

        $pagination = new ForceLengthAwarePaginator(
            collect(),
            min($total, Constants::DETAILS_FORCE_LIMIT),
            $limit,
            $total > Constants::DETAILS_FORCE_LIMIT ? Constants::DETAILS_FORCE_LIMIT : null,
            $total
        );

        if ($total <= 0) {
            return $pagination;
        }

        $items = static::getTopProductsByTrafficViewMoreByLimit(
            $from,
            $to,
            (Paginator::resolveCurrentPage() - 1) * $limit,
            $id,
            $limit,
            $total > Constants::DETAILS_FORCE_LIMIT
        );

        $pagination->setCollection($items);

        return $pagination;
    }

    /**
     * @param Carbon $from
     * @param Carbon $to
     * @param int $id
     * @return IlluminateCollection
     */
    public static function getTopProductsByTrafficViewMoreExport(Carbon $from, Carbon $to, int $id): IlluminateCollection
    {
        return static::getTopProductsByTrafficViewMoreByLimit($from, $to, null, $id, null);
    }

    /**
     * @param Carbon $from
     * @param Carbon $to
     * @param int|null $skip
     * @param int $id
     * @param int|null $limit
     * @return IlluminateCollection
     */
    protected static function getTopProductsByTrafficViewMoreByLimit(
        Carbon $from,
        Carbon $to,
        ?int $skip,
        int $id,
        ?int $limit = Constants::DETAILS_PAGINATION_LIMIT,
        bool $forceLimit = false
    ): IlluminateCollection {
        $intervals = collect(AbstractFormatter::getRangeDateForMongoDb($from, $to));
        if ($forceLimit) {
            $intervals = $intervals->slice(0, Constants::DETAILS_FORCE_LIMIT);
        }

        $intervals = $intervals->slice($skip, $limit);

        if ($intervals->isEmpty()) {
            return collect();
        }

        /** @var Connection $connection */
        $connection = \Illuminate\Support\Facades\DB::connection(Constants::CONNECTION_NAME);
        /** @var Collection|MongoCollection $collection */
        $collection = $connection->getCollection(Constants::COLLECTION_PRODUCTS);

        $match = [
            '$match' => static::getMatchFilterBase($intervals->min('from')->clone(), $intervals->max('to')->clone(), [
                [
                    'product_id' => intval($id),
                ]
            ])
        ];

        $firstGroup = [
            '$group' => [
                '_id' => [
                    '_id' => AbstractFormatter::getGroupDateForMongoDb($from->clone()->startOfDay(), $to->clone()->endOfDay()),
                ],
                'product_id' => [
                    '$first' => '$product_id',
                ],
                'product_name' => [
                    '$last' => '$product_name',
                ],
                'product_url' => [
                    '$last' => '$product_url',
                ],
                'total' => [
                    '$sum' => '$total',
                ],
                'date' => [
                    '$last' => '$date',
                ],
                'mobile' => [
                    '$sum' => [
                        '$cond' => [
                            [
                                '$eq' => [
                                    '$device', 'mobile'
                                ]
                            ],
                            '$total',
                            0
                        ],
                    ],
                ],
                'desktop' => [
                    '$sum' => [
                        '$cond' => [
                            [
                                '$ne' => [
                                    '$device', 'mobile'
                                ]
                            ],
                            '$total',
                            0
                        ],
                    ],
                ],
            ]
        ];

        $groupType = AbstractFormatter::getGroupType();
        $pipeline = [
            $match,
            $firstGroup,
            [
                '$addFields' => [
                    'device' => [
                        'mobile' => '$mobile',
                        'desktop' => '$desktop',
                        'total' => '$total',
                    ],
                    'views' => '$total',
                ],
            ],
            [
                '$project' => [
                    '_id' => 0,
                    'mobile' => 0,
                    'desktop' => 0,
                ],
            ],
        ];

        $aggregation = $collection->aggregate($pipeline, [
            'root' => 'array',
            'document' => 'array',
            'allowDiskUse' => true,
            'hint' => 'idx_dashboard',
        ]);

        return static::aggregationGroupByDateFormat($aggregation, $intervals, $groupType);
    }

    /**
     * @param Carbon $from
     * @param Carbon $to
     * @param int $id
     * @return int
     */
    public static function getTopProductsByTrafficViewMoreCount(Carbon $from, Carbon $to, int $id): int
    {
        $intervals = AbstractFormatter::getRangeDateForMongoDb($from, $to);

        return is_array($intervals) ? count($intervals) : 1;
    }
}
