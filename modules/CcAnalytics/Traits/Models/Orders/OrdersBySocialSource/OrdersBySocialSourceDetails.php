<?php

declare(strict_types=1);

namespace Modules\CcAnalytics\Traits\Models\Orders\OrdersBySocialSource;

use Carbon\Carbon;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Collection as IlluminateCollection;
use Modules\CcAnalytics\Constants;
use Modules\CcAnalytics\Helpers\ForceLengthAwarePaginator;
use MongoDB\Collection as MongoCollection;
use MongoDB\Model\BSONDocument;
use MongoDB\Collection;
use MongoDB\Laravel\Connection;

trait OrdersBySocialSourceDetails
{
    /**
     * @param Carbon $from
     * @param Carbon $to
     * @param array|null $referrerHosts
     * @param int $limit
     * @return LengthAwarePaginator
     */
    public static function getOrdersBySocialSourceDetails(Carbon $from, Carbon $to, ?array $referrerHosts = null, int $limit = Constants::DETAILS_PAGINATION_LIMIT): LengthAwarePaginator
    {
        $total = static::getOrdersBySocialSourceDetailsCount($from, $to, $referrerHosts);
        if ($total < 1) {
            return new LengthAwarePaginator(collect(), 0, $limit);
        }

        $items = static::getOrdersBySocialSourceDetailsByLimit($from, $to, (Paginator::resolveCurrentPage() - 1) * $limit, $referrerHosts, $limit);

        return new ForceLengthAwarePaginator(
            $items,
            $total,
            $limit
        );
    }

    /**
     * @param Carbon $from
     * @param Carbon $to
     * @param array|null $referrerHosts
     * @return IlluminateCollection
     */
    public static function getOrdersBySocialSourceDetailsExport(Carbon $from, Carbon $to, ?array $referrerHosts = null): IlluminateCollection
    {
        return static::getOrdersBySocialSourceDetailsByLimit($from, $to, null, $referrerHosts, null);
    }

    /**
     * @param Carbon $from
     * @param Carbon $to
     * @param int|null $skip
     * @param array|null $referrerHosts
     * @param int|null $limit
     * @return IlluminateCollection
     */
    protected static function getOrdersBySocialSourceDetailsByLimit(Carbon $from, Carbon $to, ?int $skip, ?array $referrerHosts = null, ?int $limit = Constants::DETAILS_PAGINATION_LIMIT): IlluminateCollection
    {
        /** @var Connection $connection */
        $connection = \Illuminate\Support\Facades\DB::connection(Constants::CONNECTION_NAME);
        /** @var Collection|MongoCollection $collection */
        $collection = $connection->getCollection(Constants::COLLECTION_ORDERS_SOURCES);

        $pipeline = [
            [
                '$match' => static::getMatchFilter($from->clone()->startOfDay(), $to->clone()->endOfDay(), !is_null($referrerHosts) ? [
                    [
                        'source' => [
                            '$in' => $referrerHosts,
                        ]
                    ]
                ] : [])
            ],
            [
                '$group' => [
                    '_id' => [
                        'source' => '$source',
                    ],
                    'source' => [
                        '$last' => '$source',
                    ],
                    'full_source' => [
                        '$last' => '$full_source',
                    ],
                    'campaigns' => [
                        '$addToSet' => [
                            '$cond' => [
                                'if' => ['$ne' => ['$utm_campaign', null]],
                                'then' => '$utm_campaign',
                                'else' => '$$REMOVE'
                            ]
                        ]
                    ],
                    'sales' => [
                        '$sum' => 1,
                    ],
                    'amount' => [
                        '$sum' => '$amount_without_shipping',
                    ],
                    'mobile' => [
                        '$sum' => [
                            '$cond' => [
                                [
                                    '$eq' => [
                                        '$device', 'mobile'
                                    ]
                                ],
                                1,
                                0
                            ],
                        ],
                    ],
                    'desktop' => [
                        '$sum' => [
                            '$cond' => [
                                [
                                    '$ne' => [
                                        '$device', 'mobile'
                                    ]
                                ],
                                1,
                                0
                            ],
                        ],
                    ],
                ]
            ],
            [
                '$sort' => [
                    'amount' => -1,
                    'sales' => -1,
                ]
            ],
            [
                '$addFields' => [
                    'device' => [
                        'mobile' => '$mobile',
                        'desktop' => '$desktop',
                        'total' => '$aggregate',
                    ],
                    'name' => '$source',
                    'campaigns_count' => [
                        '$size' => '$campaigns',
                    ],
                ],
            ],
            [
                '$addFields' => [
                    'viewMore' => [
                        '$cond' => [
                            [
                                '$gt' => [
                                    '$campaigns_count', 0
                                ]
                            ],
                            '$source',
                            null
                        ],
                    ],
                ],
            ],
            [
                '$project' => [
                    '_id' => 0,
                    'mobile' => 0,
                    'desktop' => 0,
                ],
            ],
        ];

        if (is_numeric($limit)) {
            $pipeline = array_merge($pipeline, [
                [
                    '$skip' => $skip,
                ],
                [
                    '$limit' => $limit,
                ],
            ]);
        }

        $aggregation = $collection->aggregate($pipeline, [
            'root' => 'array',
            'document' => 'array',
            'allowDiskUse' => true,
            'hint' => 'idx_dashboard',
        ]);

        return collect(iterator_to_array($aggregation))->map(fn(BSONDocument $document) => (new static())->newFromBuilder(static::bsonToArray($document)));
    }

    /**
     * @param Carbon $from
     * @param Carbon $to
     * @param array|null $referrerHosts
     * @return int
     */
    protected static function getOrdersBySocialSourceDetailsCount(Carbon $from, Carbon $to, ?array $referrerHosts = null): int
    {
        /** @var Connection $connection */
        $connection = \Illuminate\Support\Facades\DB::connection(Constants::CONNECTION_NAME);
        /** @var Collection|MongoCollection $collection */
        $collection = $connection->getCollection(Constants::COLLECTION_ORDERS_SOURCES);

        $pipeline = [
            [
                '$match' => static::getMatchFilter($from->clone()->startOfDay(), $to->clone()->endOfDay(), !is_null($referrerHosts) ? [
                    [
                        'source' => [
                            '$in' => $referrerHosts,
                        ]
                    ]
                ] : [])
            ],
            [
                '$group' => [
                    '_id' => [
                        'source' => '$source',
                    ],
                ],
            ],
            [
                '$group' => [
                    '_id' => null,
                    'aggregate' => [
                        '$sum' => 1,
                    ],
                ]
            ],
        ];

        $aggregation = $collection->aggregate($pipeline, [
            'root' => 'array',
            'document' => 'array',
            'allowDiskUse' => true,
            'hint' => 'idx_dashboard',
        ]);

        return iterator_to_array($aggregation)[0]->aggregate ?? 0;
    }
}
