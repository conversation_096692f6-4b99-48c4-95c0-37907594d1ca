<?php

declare(strict_types=1);

// <PERSON><PERSON>t to add $ prefix to @property annotations $in docblocks where it's missing

function processDirectory($dir)
{
    $items = scandir($dir);

    foreach ($items as $item) {
        if ($item === '.' || $item === '..') {
            continue;
        }

        $path = $dir . DIRECTORY_SEPARATOR . $item;

        if (is_dir($path)) {
            // Skip vendor directory
            if ($item === 'vendor') {
                continue;
            }
            processDirectory($path);
        } elseif (is_file($path) && pathinfo($path, PATHINFO_EXTENSION) === 'php') {
            processFile($path);
        }
    }
}

function processFile($file)
{
    $content = file_get_contents($file);

    // Skip files that don't have @property annotations $
    if (strpos($content, '@property') === false) {
        return;
    }

    // Pattern to match @property annotations $without $ prefix
    // This matches: @property <type> <name> or @property-read <type> <name> or @property-write <type> <name>
    $pattern = '/@property(-read|-write)?\s+([^\$][^\s]*)\s+([a-zA-Z_\x7f-\xff][a-zA-Z0-9_\x7f-\xff]*)/';

    // Replacement pattern that adds $ before the property name
    $replacement = '@property$1 $2 $$3';

    $newContent = preg_replace($pattern, $replacement, $content);

    // Only write to the file if changes were made
    if ($newContent !== $content) {
        file_put_contents($file, $newContent);
        echo "Updated: $file\n";
    }
}

// Start processing from the current directory
processDirectory(__DIR__);

echo "Done updating @property annotations.\n";